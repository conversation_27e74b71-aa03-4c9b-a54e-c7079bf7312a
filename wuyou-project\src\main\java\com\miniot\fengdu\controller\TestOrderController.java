package com.miniot.fengdu.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.miniot.fengdu.entity.dto.OrderListDTO;
import com.miniot.fengdu.entity.dto.CreateTestOrderRequest;
import com.miniot.fengdu.common.PageResponse;
import com.miniot.fengdu.common.OrderApiResponse;
import com.miniot.fengdu.service.TestOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 测试订单管理Controller
 */
@RestController
@RequestMapping("/back/api/admin/test-orders")
@Api(tags = "测试订单管理")
@CrossOrigin
public class TestOrderController {

    private static final Logger logger = LoggerFactory.getLogger(TestOrderController.class);

    @Autowired
    private TestOrderService testOrderService;

    /**
     * 获取测试订单列表（合并单一商品订单和购物车订单）
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取测试订单列表", notes = "获取测试表中的订单数据，支持按订单状态、订单类型、下单时间区间筛选，支持分页，不传分页参数时查询所有数据")
    public OrderApiResponse<PageResponse<OrderListDTO>> getTestOrderList(
            @ApiParam(value = "订单状态", example = "处理中") @RequestParam(required = false) String status,
            @ApiParam(value = "订单类型", example = "有礼") @RequestParam(required = false) String type,
            @ApiParam(value = "下单开始时间", example = "2023-06-01") @RequestParam(required = false) String startTime,
            @ApiParam(value = "下单结束时间", example = "2023-06-30") @RequestParam(required = false) String endTime,
            @ApiParam(value = "页码，不传则查询所有数据", example = "1") @RequestParam(required = false) Integer page,
            @ApiParam(value = "每页条数，不传则查询所有数据", example = "10") @RequestParam(required = false) Integer size) {

        try {
            // 调用测试订单查询方法
            IPage<OrderListDTO> pageResult = testOrderService.getTestOrderList(page, size, status, type, startTime, endTime);

            // 转换为PageResponse格式
            PageResponse<OrderListDTO> pageResponse = new PageResponse<>();
            pageResult.getRecords().stream().forEach(orderListDTO -> {
                if("支付成功--待收货".equals(orderListDTO.getStatus())){
                    orderListDTO.setStatus("待收货");
                }
            });
            pageResponse.setRecords(pageResult.getRecords());
            pageResponse.setTotal(pageResult.getTotal());
            pageResponse.setSize(pageResult.getSize());
            pageResponse.setCurrent(pageResult.getCurrent());
            pageResponse.setPages(pageResult.getPages());

            return OrderApiResponse.success(pageResponse);
        } catch (Exception e) {
            logger.error("获取测试订单列表失败", e);
            return OrderApiResponse.fail("获取测试订单列表失败：" + e.getMessage());
        }
    }

    /**
     * 生成测试订单数据
     */
    @PostMapping("/generate")
    @ApiOperation(value = "生成测试订单数据", notes = "批量生成测试订单数据，使用现有测试用户。订单类型：1=五有，2=有礼，3=新研，不传则随机")
    public OrderApiResponse<String> generateTestOrderData(
            @ApiParam(value = "单一商品订单数量", example = "100") @RequestParam(required = false) Integer orderCount,
            @ApiParam(value = "购物车订单数量", example = "50") @RequestParam(required = false) Integer cartOrderCount,
            @ApiParam(value = "订单类型 (1=五有，2=有礼，3=新研，不传=随机)", example = "1", allowableValues = "1,2,3") @RequestParam(required = false) Integer orderType) {

        try {
            String result = testOrderService.generateTestOrderData(orderCount, cartOrderCount, orderType);
            return OrderApiResponse.success(result);
        } catch (Exception e) {
            logger.error("生成测试订单数据失败", e);
            return OrderApiResponse.fail("生成测试订单数据失败：" + e.getMessage());
        }
    }

    /**
     * 清空测试订单数据
     */
    @DeleteMapping("/clear")
    @ApiOperation(value = "清空测试订单数据", notes = "清空所有测试表中的数据")
    public OrderApiResponse<String> clearTestOrderData() {

        try {
            String result = testOrderService.clearTestOrderData();
            return OrderApiResponse.success(result);
        } catch (Exception e) {
            logger.error("清空测试订单数据失败", e);
            return OrderApiResponse.fail("清空测试订单数据失败：" + e.getMessage());
        }
    }

    /**
     * 创建单条测试订单
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建单条测试订单", notes = "创建单条测试订单数据，订单状态默认为支付成功")
    public OrderApiResponse<String> createTestOrder(@Valid @RequestBody CreateTestOrderRequest request) {

        try {
            String result = testOrderService.createTestOrder(
                request.getOrderNo(),
                request.getUserId(),
                request.getTotalFee(),
                request.getCreateTime(),
                request.getType(),
                request.getTitle(),
                request.getProductId(),
                request.getOrderParm(),
                request.getRemark(),
                request.getUsername(),
                request.getPhone()
            );
            return OrderApiResponse.success(result);
        } catch (Exception e) {
            logger.error("创建测试订单失败", e);
            return OrderApiResponse.fail("创建测试订单失败：" + e.getMessage());
        }
    }

    /**
     * 查询测试订单信息表数据（仅查询t_order_info_test表）
     */
    @GetMapping("/info-list")
    @ApiOperation(value = "查询测试订单信息表", notes = "分页查询t_order_info_test表数据，支持用户名和手机号查询")
    public OrderApiResponse<PageResponse<OrderListDTO>> getTestOrderInfoList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(required = false) String userName,
            @RequestParam(required = false) String userPhone) {

        try {
            IPage<OrderListDTO> result = testOrderService.getTestOrderInfoList(
                page, size, status, type, startTime, endTime, userName, userPhone);

            PageResponse<OrderListDTO> pageResponse = new PageResponse<>();
            pageResponse.setRecords(result.getRecords());
            pageResponse.setTotal(result.getTotal());
            pageResponse.setSize(result.getSize());
            pageResponse.setCurrent(result.getCurrent());
            pageResponse.setPages(result.getPages());

            return OrderApiResponse.success(pageResponse);
        } catch (Exception e) {
            logger.error("查询测试订单信息表失败", e);
            return OrderApiResponse.fail("查询测试订单信息表失败：" + e.getMessage());
        }
    }
}
