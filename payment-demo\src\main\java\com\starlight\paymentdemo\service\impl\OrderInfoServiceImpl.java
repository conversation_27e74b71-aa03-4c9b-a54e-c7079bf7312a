package com.starlight.paymentdemo.service.impl;

import com.starlight.paymentdemo.entity.*;
import com.starlight.paymentdemo.enums.OrderStatus;
import com.starlight.paymentdemo.mapper.*;
import com.starlight.paymentdemo.service.OrderInfoService;
import com.starlight.paymentdemo.util.OrderNoUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@Slf4j
public class OrderInfoServiceImpl extends ServiceImpl<OrderInfoMapper, OrderInfo> implements OrderInfoService {

    @Resource
    private ProductMapper productMapper;

    @Resource
    private SkuMapper skuMapper;

    @Resource
    ResearchActivityRepository researchActivityRepository;

    @Resource
    private com.starlight.paymentdemo.repository.ResearchActivityModelRepository researchActivityModelRepository;

    @Resource
    private WyAdoptionPackageRepository wyAdoptionPackageRepository;

    /*@Resource
    private OrderInfoMapper orderInfoMapper;*/

    /*
    * 真实使用
    * */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderInfo createOrderByProductId(Long productId, String payType,Integer type) {
        //查找已存在但未支付的订单
        OrderInfo orderInfo = this.getNoPayOrderByProductId(productId);
        if( orderInfo != null){
            return orderInfo;
        }

        //获取商品信息
        //TODO：获取商品信息
        Product product=null;
        if(type== 1) {//认养
//          product = productMapper.selectById(productId);
            WyAdoptionPackage wyAdoptionPackage = wyAdoptionPackageRepository.selectById(productId);
            product = new Product();
            String price = wyAdoptionPackage.getPrice();
            //如果price有¥字符，去掉后转换为分，如果没有，则转换为分
            if(price.contains("¥")){
                price = price.replace("¥", "");
                //price转换为分
                product.setPrice(Integer.parseInt(price)*100);
                product.setId(wyAdoptionPackage.getId() + "");
                product.setTitle(wyAdoptionPackage.getName());
            }else {
                //Integer.parseInt(price)获取的值是人名币的元，需要把数据转换为分
                product.setPrice(Integer.parseInt(price) * 100);
                product.setId(wyAdoptionPackage.getId() + "");
                product.setTitle(wyAdoptionPackage.getName());
            }
        }
        else if(type== 2){//有礼
            Sku sku = skuMapper.selectById(productId);
            if(sku==null)
                throw new RuntimeException("新研商品未找到");

            product = new Product();
            product.setPrice(sku.getPrice());
            product.setId(sku.getId());
            product.setTitle(sku.getName());
             }
        else if(type== 3) {//新研（智能识别活动ID或规格ID）

            // 先尝试作为规格ID查询
            ResearchActivityModel researchActivityModel = researchActivityModelRepository.selectById(productId);
            if(researchActivityModel != null) {
                // 找到规格商品，使用规格信息
                product = new Product();
                BigDecimal bd = researchActivityModel.getPrice();
                Integer result = bd.multiply(BigDecimal.valueOf(100)).intValueExact();
                product.setPrice(result);
                product.setId(researchActivityModel.getId()+"");
                product.setTitle(researchActivityModel.getTitle());
            } else {
                // 作为活动ID查询（兼容旧版本）
                ResearchActivity researchActivity = researchActivityRepository.selectById(productId);
                if(researchActivity==null)
                    throw new RuntimeException("新研商品未找到");

                product = new Product();
                BigDecimal bd = researchActivity.getPrice();
                Integer result = bd.multiply(BigDecimal.valueOf(100)).intValueExact();
                product.setPrice(result);
                product.setId(researchActivity.getId()+"");
                product.setTitle(researchActivity.getTitle());
            }
        }else{
            product = productMapper.selectById(productId);
        }

        //生成订单
        orderInfo = new OrderInfo();
//        orderInfo.setTitle(product.getTitle());
//        orderInfo.setOrderNo(OrderNoUtils.getOrderNo()); //订单号
//        orderInfo.setProductId(productId);
//        orderInfo.setTotalFee(product.getPrice()); //分
//        orderInfo.setOrderStatus(OrderStatus.NOTPAY.getType());
//        baseMapper.insert(orderInfo);
//

        orderInfo.setTitle(product.getTitle());
        orderInfo.setType(type);
        orderInfo.setTypeResult(type);
        orderInfo.setOrderNo(OrderNoUtils.getOrderNo()); //订单号
//        orderInfo.setOpenId(openid);
//        orderInfo.setUserId(userId);

        orderInfo.setProductId(productId);
        orderInfo.setTotalFee(product.getPrice()); //分
        orderInfo.setOrderStatus(OrderStatus.NOTPAY.getType());
        baseMapper.insert(orderInfo);

        return orderInfo;
    }
    public static int convertAndMultiply(String priceStr) {
        if (priceStr == null || priceStr.trim().isEmpty()) {
            return 0; // 处理空值
        }

        try {
            // 使用 BigDecimal 处理各种数字格式，避免浮点数精度问题
            BigDecimal price = new BigDecimal(priceStr.trim());
            // 乘以 100 后取整（向下取整，如 0.009 → 0）
            return price.multiply(new BigDecimal("100")).intValue();
        } catch (NumberFormatException e) {
            // 处理非数字格式
            System.err.println("Invalid price format: " + priceStr);
            return 0; // 或根据业务需求抛异常
        }
    }

    /*
    * 真实使用
    * */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderInfo createOrderByProductId(Long productId, String payType,Integer type,String openid,Long userId,PayParm parm) {
        //查找已存在但未支付的订单
        OrderInfo orderInfo = this.getNoPayOrderByProductId(productId);
        if (orderInfo != null) {
            return orderInfo;
        }


        //获取商品信息
        //TODO：获取商品信息
        Product product = null;
        if (type == 1){//五有
//            product = productMapper.selectById(productId);
            WyAdoptionPackage wyAdoptionPackage = wyAdoptionPackageRepository.selectById(productId);
        product = new Product();
        String price = wyAdoptionPackage.getPrice();
        //如果price有¥字符，去掉后转换为分，如果没有，则转换为分
        if(price.contains("¥")){
            price = price.replace("¥", "");
            //price转换为分
            product.setPrice(convertAndMultiply(price));
            product.setId(wyAdoptionPackage.getId() + "");
            product.setTitle(wyAdoptionPackage.getName());
        }else {
            //Integer.parseInt(price)获取的值是人名币的元，需要把数据转换为分
//如果price为两位数小数如何转换

            product.setPrice(convertAndMultiply(price));
            product.setId(wyAdoptionPackage.getId() + "");
            product.setTitle(wyAdoptionPackage.getName());
        }
    }
        else if(type== 2){//有礼



            Sku sku = skuMapper.selectById(productId);
            if(sku==null)
                throw new RuntimeException("新研商品未找到");

            product = new Product();
            product.setPrice(sku.getPrice());
            product.setId(sku.getId());
            product.setTitle(sku.getName());
        }
        else if(type== 3) {//新研

            ResearchActivity researchActivity = researchActivityRepository.selectById(productId);
            if(researchActivity==null)
                throw new RuntimeException("新研商品未找到");

            product = new Product();
            BigDecimal bd = researchActivity.getPrice();
            Integer result = bd.multiply(BigDecimal.valueOf(100)).intValueExact();
            product.setPrice(result);
            product.setId(researchActivity.getId()+"");
            product.setTitle(researchActivity.getTitle());
        }
        else if(type== 4) {//购物车订单
            // 购物车订单的商品信息从PayParm中获取
            product = new Product();
            product.setId(productId.toString());
            product.setTitle("购物车订单"); // 默认标题，实际金额从parm中获取
            product.setPrice(0); // 价格将从PayParm中解析
        }
        else{
            product = productMapper.selectById(productId);
        }

        //生成订单
        orderInfo = new OrderInfo();

        // 处理购物车订单的特殊逻辑
        if(type == 4) {
            // 购物车订单：从小程序服务传来的订单号和金额
            String cartOrderNo = parm.getParms(); // 购物车订单号
            log.info("创建购物车支付订单，购物车订单号：{}", cartOrderNo);

            orderInfo.setTitle("购物车订单");
            orderInfo.setOrderNo(cartOrderNo); // 使用小程序传来的订单号

            // 🔧 修复：从备注中解析总金额，避免金额为0导致回调失败
            Integer totalAmount = null;
            try {
                if (parm.getRemark() != null && parm.getRemark().contains("AMOUNT:")) {
                    String remark = parm.getRemark();
                    int amountIndex = remark.indexOf("AMOUNT:");
                    if (amountIndex >= 0) {
                        String amountPart = remark.substring(amountIndex + 7); // 去掉"AMOUNT:"前缀
                        // 如果有其他信息，用|分隔，只取金额部分
                        if (amountPart.contains("|")) {
                            amountPart = amountPart.substring(0, amountPart.indexOf("|"));
                        }
                        totalAmount = Integer.parseInt(amountPart.trim());
                        log.info("从备注中解析到购物车订单金额：{}分，原备注：{}", totalAmount, remark);
                    }
                }
            } catch (Exception e) {
                log.warn("解析购物车订单金额失败，备注：{}，错误：{}", parm.getRemark(), e.getMessage());
            }

            // 如果无法从备注解析金额，则从小程序服务获取
            if (totalAmount == null || totalAmount <= 0) {
                log.warn("购物车订单金额无效，需要从小程序服务获取，订单号：{}", cartOrderNo);
                // 这里应该调用小程序服务获取订单金额，暂时设为1分避免支付失败
                totalAmount = 1; // 临时设置，实际应该从小程序服务获取
            }

            orderInfo.setTotalFee(totalAmount);
        } else {
            // 单个商品订单：使用原有逻辑
            orderInfo.setTitle(product.getTitle());
            orderInfo.setOrderNo(OrderNoUtils.getOrderNo()); //订单号
            orderInfo.setTotalFee(product.getPrice()); //分
        }

        orderInfo.setType(type);
        orderInfo.setTypeResult(type);
        orderInfo.setOpenId(openid);
        orderInfo.setUserId(userId);
        orderInfo.setProductId(productId);
        orderInfo.setOrderStatus(OrderStatus.NOTPAY.getType());
        orderInfo.setRemark(parm.getRemark());
        orderInfo.setOrderParm(parm.getParms());

        // 购物车订单不在支付服务的数据库中创建记录，只在内存中构建用于支付
        if (type != 4) {
            baseMapper.insert(orderInfo);
        } else {
            log.info("购物车订单不在支付服务数据库中创建记录，订单号：{}", orderInfo.getOrderNo());
        }

        return orderInfo;
    }


    /**
     * 存储订单二维码
     * @param orderNo
     * @param codeUrl
     */
    @Override
    public void saveCodeUrl(String orderNo, String codeUrl) {

        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", orderNo);

        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setCodeUrl(codeUrl);

        baseMapper.update(orderInfo, queryWrapper);
    }

    /**
     * 查询订单列表，并倒序查询
     * @return
     */
    @Override
    public List<OrderInfo> listOrderByCreateTimeDesc() {

        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<OrderInfo>().orderByDesc("create_time");
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 根据订单号更新订单状态
     * @param orderNo
     * @param orderStatus
     */
    @Override
    public void updateStatusByOrderNo(String orderNo, OrderStatus orderStatus) {

        log.info("更新订单状态 ===> {}", orderStatus.getType());

        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", orderNo);

        OrderInfo orderInfo2 = new OrderInfo();
        orderInfo2.setOrderStatus(orderStatus.getType());

        baseMapper.update(orderInfo2, queryWrapper);
    }

    @Autowired
    UserBehaviorStatsRepository userBehaviorStatsRepository;
    /**
     * 根据订单号获取订单状态
     * @param orderNo
     * @return
     */
    @Override
    public String getOrderStatus(String orderNo) {

        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", orderNo);
        OrderInfo orderInfo = baseMapper.selectOne(queryWrapper);
        if(orderInfo == null){
            return null;
        }
        if(OrderStatus.NOTPAY.getType().equals( orderInfo.getOrderStatus()))
        {
            Long userId = orderInfo.getUserId();
            QueryWrapper<UserBehaviorStats> queryWrapper2= new QueryWrapper<>();
            queryWrapper2.eq("user_id", userId);
            UserBehaviorStats userBehaviorStats = userBehaviorStatsRepository.selectOne(queryWrapper2);
            if(userBehaviorStats==null){
                userBehaviorStats = new UserBehaviorStats();
                userBehaviorStats.setUserId(userId);
                userBehaviorStats.setActivityCount(0);
                userBehaviorStats.setAdoptCount(0);
                if(orderInfo.getType()==1){
                    userBehaviorStats.setAdoptCount(1);
                    userBehaviorStats.setLastAdoptTime(LocalDateTime.now());
                }else if(orderInfo.getType()==2){
                    userBehaviorStats.setPurchaseCount(1);
                    userBehaviorStats.setLastPurchaseTime(LocalDateTime.now());
                }else if(orderInfo.getType()==3){
                    userBehaviorStats.setActivityCount(1);
                    userBehaviorStats.setLastActivityTime(LocalDateTime.now());
                }
                userBehaviorStats.setCreatedAt(LocalDateTime.now());
                userBehaviorStats.setUpdatedAt(LocalDateTime.now());
                userBehaviorStatsRepository.insert(userBehaviorStats);

            }
            else{
                if(orderInfo.getType()==1){
                    userBehaviorStats.setAdoptCount(userBehaviorStats.getAdoptCount()+1);
                    userBehaviorStats.setLastAdoptTime(LocalDateTime.now());
                }else if(orderInfo.getType()==2){
                    userBehaviorStats.setPurchaseCount(userBehaviorStats.getPurchaseCount()+1);
                    userBehaviorStats.setLastPurchaseTime(LocalDateTime.now());
                }else if(orderInfo.getType()==3){
                    userBehaviorStats.setActivityCount(userBehaviorStats.getActivityCount()+1);
                    userBehaviorStats.setLastActivityTime(LocalDateTime.now());
                }
                userBehaviorStats.setUpdatedAt(LocalDateTime.now());
                userBehaviorStatsRepository.update(userBehaviorStats, queryWrapper2);
            }
        }

        return orderInfo.getOrderStatus();
    }

    /**
     * 查询创建超过minutes分钟并且未支付的订单
     * @param minutes
     * @return
     */
    @Override
    public List<OrderInfo> getNoPayOrderByDuration(int minutes) {

        // 使用传入的minutes参数，而不是硬编码1分钟
        Instant instant = Instant.now().minus(Duration.ofMinutes(minutes));
        ZonedDateTime chinaTime = instant.atZone(ZoneId.of("Asia/Shanghai"));

        // 格式化输出
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedTime = chinaTime.format(formatter);

        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_status", OrderStatus.NOTPAY.getType());
        queryWrapper.le("create_time", formattedTime);

        List<OrderInfo> orderInfoList = baseMapper.selectList(queryWrapper);

        return orderInfoList;
    }

    /**
     * 根据订单号获取订单
     * @param orderNo
     * @return
     */
    @Override
    public OrderInfo getOrderByOrderNo(String orderNo) {

        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", orderNo);
        OrderInfo orderInfo = baseMapper.selectOne(queryWrapper);

        return orderInfo;
    }


    /**
     * 根据商品id查询未支付订单
     * 防止重复创建订单对象
     * @param productId
     * @return
     */
    private OrderInfo getNoPayOrderByProductId(Long productId) {

        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("product_id", productId);
        queryWrapper.eq("order_status", OrderStatus.NOTPAY.getType());
//        queryWrapper.eq("user_id", userId);
        OrderInfo orderInfo = baseMapper.selectOne(queryWrapper);
        return orderInfo;
    }
}
