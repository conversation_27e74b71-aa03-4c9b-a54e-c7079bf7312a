package com.miniot.fengdu.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miniot.fengdu.common.ApiResponse;
import com.miniot.fengdu.common.BaseController;
import com.miniot.fengdu.entity.Content;
import com.miniot.fengdu.service.ContentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 后台内容管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/back/api/admin/content")
@Api(tags = "后台内容管理接口")
public class ContentController extends BaseController {

    @Autowired
    private ContentService contentService;

    @GetMapping("/list")
    @ApiOperation(value = "获取内容列表", notes = "获取内容列表，支持多种筛选条件。不传分页参数时查询所有数据")
    public ApiResponse<Page<Content>> getContentList(
            @ApiParam(value = "页码，不传则查询所有数据") @RequestParam(required = false) Integer page,
            @ApiParam(value = "每页大小，不传则查询所有数据") @RequestParam(required = false) Integer size,
            @ApiParam(value = "内容类型", allowableValues = "article,video,topic") @RequestParam(required = false) String type,
            @ApiParam(value = "状态", allowableValues = "0,1,2,3") @RequestParam(required = false) Integer status,
            @ApiParam(value = "作者ID") @RequestParam(required = false) Long authorId,
            @ApiParam(value = "标题关键词") @RequestParam(required = false) String title,
            @ApiParam(value = "作者关键词") @RequestParam(required = false) String author) {

        // 如果不传分页参数，则查询所有数据
        if (page == null || size == null) {
            page = 1;
            size = Integer.MAX_VALUE; // 设置为最大值，实际查询所有数据
        }

        Page<Content> contentPage = contentService.getContentPage(page, size, type, status, authorId, title, author, null);
        return ApiResponse.success(contentPage, "获取内容列表成功");
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "获取内容详情", notes = "根据ID获取内容详细信息")
    public ApiResponse<Content> getContentDetail(
            @ApiParam(value = "内容ID", required = true) @PathVariable Long id) {
        
        Content content = contentService.getContentDetail(id);
        if (content == null) {
            return ApiResponse.fail("内容不存在");
        }
        return ApiResponse.success(content, "获取内容详情成功");
    }

    @PostMapping
    @ApiOperation(value = "创建内容", notes = "管理员创建新内容，默认为已发布状态")
    public ApiResponse<Void> createContent(@Valid @RequestBody Content content) {
        // 设置管理员作为作者（这里可以从当前登录用户获取）
        if (content.getAuthor() == null) {
            content.setAuthor("管理员");
        }
        if (content.getAuthorId() == null) {
            content.setAuthorId(0L); // 管理员ID设为0
        }
        
        boolean result = contentService.createContent(content);
        return result 
                ? ApiResponse.success(null, "内容创建成功") 
                : ApiResponse.fail("内容创建失败");
    }

    @PutMapping("/{id}")
    @ApiOperation(value = "更新内容", notes = "更新指定ID的内容信息")
    public ApiResponse<Void> updateContent(
            @ApiParam(value = "内容ID", required = true) @PathVariable Long id,
            @Valid @RequestBody Content content) {
        
        content.setId(id);
        boolean result = contentService.updateContent(content);
        return result 
                ? ApiResponse.success(null, "内容更新成功") 
                : ApiResponse.fail("内容更新失败");
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除内容", notes = "删除指定ID的内容")
    public ApiResponse<Void> deleteContent(
            @ApiParam(value = "内容ID", required = true) @PathVariable Long id) {
        
        boolean result = contentService.deleteContent(id);
        return result 
                ? ApiResponse.success(null, "内容删除成功") 
                : ApiResponse.fail("内容删除失败");
    }




}
