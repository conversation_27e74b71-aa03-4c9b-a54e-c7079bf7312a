package com.miniot.fengdu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miniot.fengdu.dto.DemoSiteMediaDTO;
import com.miniot.fengdu.dto.DemoSiteMediaListDTO;
import com.miniot.fengdu.entity.DemoSite;
import com.miniot.fengdu.entity.DemoSiteMedia;
import com.miniot.fengdu.repository.DemoSiteMediaRepository;
import com.miniot.fengdu.repository.DemoSiteRepository;
import com.miniot.fengdu.service.DemoSiteMediaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 示范点媒体服务实现
 */
@Service
@Slf4j
public class DemoSiteMediaServiceImpl extends ServiceImpl<DemoSiteMediaRepository, DemoSiteMedia> implements DemoSiteMediaService {
    
    @Autowired
    private DemoSiteRepository demoSiteRepository;
    
    @Override
    public DemoSiteMediaListDTO getDemoSiteMediaList(Long demoSiteId) {
        // 获取示范点信息
        DemoSite demoSite = demoSiteRepository.selectById(demoSiteId);
        if (demoSite == null) {
            return null;
        }
        
        // 获取媒体列表
        List<DemoSiteMedia> mediaList = baseMapper.findByDemoSiteIdOrderBySortOrder(demoSiteId);
        
        // 统计媒体数量
        Map<String, Integer> typeCountMap = new HashMap<>();
        typeCountMap.put(DemoSiteMedia.MediaType.IMAGE, 0);
        typeCountMap.put(DemoSiteMedia.MediaType.VIDEO, 0);
        
        for (DemoSiteMedia media : mediaList) {
            typeCountMap.put(media.getMediaType(), 
                typeCountMap.get(media.getMediaType()) + 1);
        }
        
        // 构建响应DTO
        DemoSiteMediaListDTO result = new DemoSiteMediaListDTO();
        result.setDemoSiteId(demoSiteId);
        result.setDemoSiteName(demoSite.getName());
        result.setTotalCount(mediaList.size());
        result.setImageCount(typeCountMap.get(DemoSiteMedia.MediaType.IMAGE));
        result.setVideoCount(typeCountMap.get(DemoSiteMedia.MediaType.VIDEO));
        result.setMediaList(mediaList.stream().map(this::convertToDTO).collect(Collectors.toList()));
        
        return result;
    }
    
    @Override
    public List<DemoSiteMediaDTO> getDemoSiteMediaByType(Long demoSiteId, String mediaType) {
        List<DemoSiteMedia> mediaList = baseMapper.findByDemoSiteIdAndMediaType(demoSiteId, mediaType);
        return mediaList.stream().map(this::convertToDTO).collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public Long addMedia(DemoSiteMediaDTO demoSiteMediaDTO) {
        DemoSiteMedia media = new DemoSiteMedia();
        convertToEntity(demoSiteMediaDTO, media);
        
        // 如果没有指定排序顺序，自动获取下一个排序号
        if (media.getSortOrder() == null) {
            Integer nextSortOrder = baseMapper.getNextSortOrder(media.getDemoSiteId());
            media.setSortOrder(nextSortOrder);
        }
        
        media.setCreatedAt(LocalDateTime.now());
        media.setUpdatedAt(LocalDateTime.now());
        
        save(media);
        return media.getId();
    }
    
    @Override
    @Transactional
    public boolean updateMedia(Long id, DemoSiteMediaDTO demoSiteMediaDTO) {
        DemoSiteMedia media = getById(id);
        if (media == null) {
            return false;
        }
        
        convertToEntity(demoSiteMediaDTO, media);
        media.setId(id);
        media.setUpdatedAt(LocalDateTime.now());
        
        return updateById(media);
    }
    
    @Override
    @Transactional
    public boolean deleteMedia(Long id) {
        return removeById(id);
    }
    
    @Override
    @Transactional
    public boolean batchDeleteMedia(List<Long> ids) {
        return removeByIds(ids);
    }
    
    @Override
    @Transactional
    public boolean updateMediaSort(List<Long> mediaIds) {
        try {
            for (int i = 0; i < mediaIds.size(); i++) {
                Long mediaId = mediaIds.get(i);
                Integer sortOrder = i + 1;
                baseMapper.updateSortOrder(mediaId, sortOrder);
            }
            return true;
        } catch (Exception e) {
            log.error("更新媒体排序失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public DemoSiteMediaDTO getMediaDetail(Long id) {
        DemoSiteMedia media = getById(id);
        return media != null ? convertToDTO(media) : null;
    }
    
    /**
     * 实体转DTO
     */
    private DemoSiteMediaDTO convertToDTO(DemoSiteMedia entity) {
        DemoSiteMediaDTO dto = new DemoSiteMediaDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
    
    /**
     * DTO转实体
     */
    private void convertToEntity(DemoSiteMediaDTO dto, DemoSiteMedia entity) {
        BeanUtils.copyProperties(dto, entity);
    }
}
