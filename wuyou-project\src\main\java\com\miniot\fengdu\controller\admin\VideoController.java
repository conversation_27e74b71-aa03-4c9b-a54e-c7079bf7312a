package com.miniot.fengdu.controller.admin;

import com.miniot.fengdu.common.ApiResponse;
import com.miniot.fengdu.common.BaseController;
import com.miniot.fengdu.service.OSSService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 媒体文件管理控制器（图片、视频）
 */
@RestController
@RequestMapping("/back/api/media")
@Api(tags = "媒体文件管理接口")
@Slf4j
public class VideoController extends BaseController {

    @Autowired
    private OSSService ossService;

    @Value("${admin.api.key}")
    private String adminApiKey;

    /**
     * 验证API密钥
     */
    private boolean validateApiKey(String apiKey) {
        if (apiKey == null || !adminApiKey.equals(apiKey)) {
            log.warn("无效的API密钥访问尝试: {}", apiKey);
            return false;
        }
        return true;
    }

    /**
     * 上传图片文件
     */
    @PostMapping("/upload/image")
    @ApiOperation(value = "上传图片", notes = "上传图片文件到联通云OSS")
    public ApiResponse<Map<String, Object>> uploadImage(
            @RequestHeader("X-Admin-Key") String apiKey,
            @ApiParam(value = "图片文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "存储目录", example = "demo-sites") @RequestParam(value = "directory", defaultValue = "demo-sites") String directory) {

        // 验证API密钥
        if (!validateApiKey(apiKey)) {
            return ApiResponse.fail("无效的API密钥，访问被拒绝");
        }

        try {
            // 验证文件类型
            String fileName = file.getOriginalFilename();
            if (fileName == null || !isImageFile(fileName)) {
                return ApiResponse.fail("请上传有效的图片文件（支持jpg、jpeg、png、gif、bmp、webp格式）");
            }

            log.info("开始上传图片文件: {}, 大小: {}", fileName, file.getSize());
            Map<String, Object> result = ossService.uploadVideo(file, directory);
            return ApiResponse.success(result, "图片上传成功");
        } catch (IOException e) {
            log.error("图片上传失败: {}", e.getMessage(), e);
            return ApiResponse.fail("图片上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传视频文件
     */
    @PostMapping("/upload/video")
    @ApiOperation(value = "上传视频", notes = "上传视频文件到联通云OSS")
    public ApiResponse<Map<String, Object>> uploadVideo(
            @RequestHeader("X-Admin-Key") String apiKey,
            @ApiParam(value = "视频文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "存储目录", example = "demo-sites") @RequestParam(value = "directory", defaultValue = "demo-sites") String directory) {

        // 验证API密钥
        if (!validateApiKey(apiKey)) {
            return ApiResponse.fail("无效的API密钥，访问被拒绝");
        }

        try {
            // 验证文件类型
            String fileName = file.getOriginalFilename();
            if (fileName == null || !isVideoFile(fileName)) {
                return ApiResponse.fail("请上传有效的视频文件（支持mp4、avi、wmv、flv、mov、mkv格式）");
            }

            log.info("开始上传视频文件: {}, 大小: {}", fileName, file.getSize());
            Map<String, Object> result = ossService.uploadVideo(file, directory);
            return ApiResponse.success(result, "视频上传成功");
        } catch (IOException e) {
            log.error("视频上传失败: {}", e.getMessage(), e);
            return ApiResponse.fail("视频上传失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件（支持URL或ObjectKey）
     */
    @DeleteMapping("/delete")
    @ApiOperation(value = "删除文件", notes = "删除OSS上的文件（支持完整URL或ObjectKey）")
    public ApiResponse<Boolean> deleteFile(
            @RequestHeader("X-Admin-Key") String apiKey,
            @ApiParam(value = "文件URL或ObjectKey", required = true) @RequestParam("fileUrlOrKey") String fileUrlOrKey) {

        // 验证API密钥
        if (!validateApiKey(apiKey)) {
            return ApiResponse.fail("无效的API密钥，访问被拒绝");
        }

        try {
            boolean result = ossService.deleteFile(fileUrlOrKey);
            return result ?
                ApiResponse.success(true, "文件删除成功") :
                ApiResponse.fail("文件删除失败");
        } catch (Exception e) {
            log.error("删除文件失败: {}", e.getMessage(), e);
            return ApiResponse.fail("删除文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件URL
     */
    @GetMapping("/url")
    @ApiOperation(value = "获取文件URL", notes = "获取联通云OSS上文件的访问URL")
    public ApiResponse<String> getFileUrl(
            @ApiParam(value = "对象键", required = true, example = "demo-sites/2024/07/30/xxx.mp4")
            @RequestParam("objectKey") String objectKey) {

        try {
            String url = ossService.getFileUrl(objectKey);
            if (url == null) {
                return ApiResponse.fail("文件不存在: " + objectKey);
            }
            return ApiResponse.success(url, "获取文件URL成功");
        } catch (Exception e) {
            log.error("获取文件URL失败: {}", e.getMessage(), e);
            return ApiResponse.fail("获取文件URL失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取预签名上传URL
     */
    @GetMapping("/presigned-url")
    @ApiOperation(value = "获取预签名URL", notes = "获取用于上传文件的预签名URL")
    public ApiResponse<String> getPresignedUrl(
            @ApiParam(value = "对象键", required = true, example = "videos/test.mp4") 
            @RequestParam("objectKey") String objectKey,
            @ApiParam(value = "过期时间（秒）", example = "3600") 
            @RequestParam(value = "expireTime", defaultValue = "3600") long expireTime) {
        
        String url = ossService.generatePresignedUrl(objectKey, expireTime);
        return ApiResponse.success(url, "获取预签名URL成功");
    }
    
    /**
     * 创建Bucket
     */
    @PostMapping("/bucket/create")
    @ApiOperation(value = "创建Bucket", notes = "创建新的OSS Bucket")
    public ApiResponse<Boolean> createBucket(
            @RequestHeader("X-Admin-Key") String apiKey,
            @ApiParam(value = "Bucket名称", required = true)
            @RequestParam("bucketName") String bucketName) {

        // 验证API密钥
        if (!validateApiKey(apiKey)) {
            return ApiResponse.fail("无效的API密钥，访问被拒绝");
        }

        boolean result = ossService.createBucket(bucketName);
        return result ? 
            ApiResponse.success(true, "创建Bucket成功") : 
            ApiResponse.fail("创建Bucket失败");
    }
    
    /**
     * 获取Bucket列表
     */
    @GetMapping("/bucket/list")
    @ApiOperation(value = "获取Bucket列表", notes = "获取所有可用的Bucket列表")
    public ApiResponse<List<String>> listBuckets(@RequestHeader("X-Admin-Key") String apiKey) {
        // 验证API密钥
        if (!validateApiKey(apiKey)) {
            return ApiResponse.fail("无效的API密钥，访问被拒绝");
        }

        List<String> buckets = ossService.listBuckets();
        return ApiResponse.success(buckets, "获取Bucket列表成功");
    }

    /**
     * 验证是否为图片文件
     */
    private boolean isImageFile(String fileName) {
        String extension = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
        return extension.equals(".jpg") || extension.equals(".jpeg") ||
               extension.equals(".png") || extension.equals(".gif") ||
               extension.equals(".bmp") || extension.equals(".webp");
    }

    /**
     * 验证是否为视频文件
     */
    private boolean isVideoFile(String fileName) {
        String extension = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
        return extension.equals(".mp4") || extension.equals(".avi") ||
               extension.equals(".wmv") || extension.equals(".flv") ||
               extension.equals(".mov") || extension.equals(".mkv");
    }
}