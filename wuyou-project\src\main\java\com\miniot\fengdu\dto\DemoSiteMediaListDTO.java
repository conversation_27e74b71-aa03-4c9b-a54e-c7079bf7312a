package com.miniot.fengdu.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 示范点媒体列表DTO
 */
@Data
@ApiModel(value = "示范点媒体列表DTO", description = "示范点媒体列表响应对象")
public class DemoSiteMediaListDTO {
    
    @ApiModelProperty(value = "示范点ID", example = "1")
    private Long demoSiteId;
    
    @ApiModelProperty(value = "示范点名称", example = "富春江示范点")
    private String demoSiteName;
    
    @ApiModelProperty(value = "媒体总数", example = "5")
    private Integer totalCount;
    
    @ApiModelProperty(value = "图片数量", example = "3")
    private Integer imageCount;
    
    @ApiModelProperty(value = "视频数量", example = "2")
    private Integer videoCount;
    
    @ApiModelProperty(value = "媒体列表")
    private List<DemoSiteMediaDTO> mediaList;
}
