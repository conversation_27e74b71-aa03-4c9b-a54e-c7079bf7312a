package com.miniot.fengdu.service.impl;

import com.amazonaws.HttpMethod;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.AmazonClientException;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.Bucket;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.miniot.fengdu.config.OSSConfig;
import com.miniot.fengdu.service.OSSService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
@Slf4j
public class OSSServiceImpl implements OSSService {

    @Autowired
    private AmazonS3Client amazonS3Client;

    @Autowired
    private OSSConfig ossConfig;

    @Override
    public Map<String, Object> uploadVideo(MultipartFile file, String directory) throws IOException {
        // 检查文件是否为空
        if (file.isEmpty()) {
            throw new IllegalArgumentException("上传文件不能为空");
        }

        // 获取文件原始名称
        String originalFilename = file.getOriginalFilename();
        
        // 生成唯一文件名
        String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        String newFileName = UUID.randomUUID().toString().replaceAll("-", "") + suffix;
        
        // 按日期生成目录
        String datePath = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
        String objectKey = directory + "/" + datePath + "/" + newFileName;
        
        // 设置文件元数据
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType(getContentType(suffix));
        metadata.setContentLength(file.getSize());
        
        // 创建上传请求
        PutObjectRequest request = new PutObjectRequest(
                ossConfig.getBucketName(),
                objectKey,
                file.getInputStream(),
                metadata
        ).withCannedAcl(CannedAccessControlList.PublicRead); // 设置为公共读取权限
        
        // 执行上传
        PutObjectResult result = amazonS3Client.putObject(request);
        log.info("文件上传成功，ETag: {}", result.getETag());
        
        // 构建返回结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("url", getFileUrl(objectKey));
        resultMap.put("objectKey", objectKey);
        resultMap.put("fileName", originalFilename);
        resultMap.put("fileSize", file.getSize());
        
        return resultMap;
    }

    @Override
    public boolean deleteFile(String fileUrlOrKey) {
        try {
            // 智能判断是URL还是ObjectKey
            String objectKey = extractObjectKey(fileUrlOrKey);

            amazonS3Client.deleteObject(ossConfig.getBucketName(), objectKey);
            log.info("文件删除成功: {}", objectKey);
            return true;
        } catch (Exception e) {
            log.error("文件删除失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getFileUrl(String objectKey) {
        try {
            // 检查文件是否存在
            if (!amazonS3Client.doesObjectExist(ossConfig.getBucketName(), objectKey)) {
                log.warn("文件不存在: {}", objectKey);
                return null;
            }

            return ossConfig.getBucketUrl() + "/" + objectKey;
        } catch (Exception e) {
            log.error("获取文件URL失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String generatePresignedUrl(String objectKey, long expireTime) {
        Date expiration = new Date();
        expiration.setTime(expiration.getTime() + expireTime * 1000);
        
        URL url = amazonS3Client.generatePresignedUrl(
                ossConfig.getBucketName(), 
                objectKey, 
                expiration,
                HttpMethod.PUT
        );
        
        return url.toString();
    }
    
    @Override
    public boolean createBucket(String bucketName) {
        try {
            log.info("开始创建Bucket: {}", bucketName);
            amazonS3Client.createBucket(bucketName);
            log.info("创建Bucket成功: {}", bucketName);
            return true;
        } catch (AmazonServiceException ase) {
            log.error("创建Bucket失败，服务端错误: {}", ase.getMessage(), ase);
            log.error("错误码: {}, HTTP状态码: {}, AWS错误码: {}, 错误类型: {}, 请求ID: {}", 
                    ase.getMessage(), ase.getStatusCode(), ase.getErrorCode(), 
                    ase.getErrorType(), ase.getRequestId());
            return false;
        } catch (AmazonClientException ace) {
            log.error("创建Bucket失败，客户端错误: {}", ace.getMessage(), ace);
            return false;
        }
    }
    
    @Override
    public List<String> listBuckets() {
        try {
            log.info("开始获取Bucket列表");
            List<Bucket> buckets = amazonS3Client.listBuckets();
            List<String> bucketNames = new ArrayList<>();
            
            for (Bucket bucket : buckets) {
                bucketNames.add(bucket.getName());
                log.info("Bucket: {}, 创建时间: {}", bucket.getName(), bucket.getCreationDate());
            }
            
            return bucketNames;
        } catch (Exception e) {
            log.error("获取Bucket列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 智能提取ObjectKey（私有方法）
     * 支持完整URL或直接的ObjectKey
     */
    private String extractObjectKey(String fileUrlOrKey) {
        if (fileUrlOrKey == null || fileUrlOrKey.trim().isEmpty()) {
            log.warn("文件URL或ObjectKey为空");
            return null;
        }

        String bucketUrl = ossConfig.getBucketUrl();
        if (fileUrlOrKey.startsWith(bucketUrl)) {
            // 从完整URL中提取ObjectKey
            // 例如：https://wuyou.obs-cq.cucloud.cn/demo-sites/2024/01/15/abc123.jpg
            // 提取：demo-sites/2024/01/15/abc123.jpg
            String objectKey = fileUrlOrKey.substring(bucketUrl.length());
            // 去掉开头的斜杠
            if (objectKey.startsWith("/")) {
                objectKey = objectKey.substring(1);
            }
            log.debug("从URL提取ObjectKey: {} -> {}", fileUrlOrKey, objectKey);
            return objectKey;
        } else {
            // 如果不是完整URL，直接当作ObjectKey使用
            log.debug("直接使用ObjectKey: {}", fileUrlOrKey);
            return fileUrlOrKey;
        }
    }

    /**
     * 根据文件后缀获取MIME类型
     */
    private String getContentType(String suffix) {
        suffix = suffix.toLowerCase();

        // 视频类型
        if (suffix.equals(".mp4")) {
            return "video/mp4";
        } else if (suffix.equals(".avi")) {
            return "video/x-msvideo";
        } else if (suffix.equals(".wmv")) {
            return "video/x-ms-wmv";
        } else if (suffix.equals(".flv")) {
            return "video/x-flv";
        } else if (suffix.equals(".mov")) {
            return "video/quicktime";
        } else if (suffix.equals(".mkv")) {
            return "video/x-matroska";
        }
        // 图片类型
        else if (suffix.equals(".jpg") || suffix.equals(".jpeg")) {
            return "image/jpeg";
        } else if (suffix.equals(".png")) {
            return "image/png";
        } else if (suffix.equals(".gif")) {
            return "image/gif";
        } else if (suffix.equals(".bmp")) {
            return "image/bmp";
        } else if (suffix.equals(".webp")) {
            return "image/webp";
        } else {
            return "application/octet-stream";
        }
    }
} 