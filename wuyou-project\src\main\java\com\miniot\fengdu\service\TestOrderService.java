package com.miniot.fengdu.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.miniot.fengdu.entity.dto.OrderListDTO;

/**
 * 测试订单服务接口
 */
public interface TestOrderService {

    /**
     * 分页查询测试订单列表（合并单一商品订单和购物车订单）
     * @param page 页码
     * @param size 每页条数
     * @param status 订单状态
     * @param type 订单类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 合并订单列表分页数据
     */
    IPage<OrderListDTO> getTestOrderList(Integer page, Integer size, String status, String type, String startTime, String endTime);

    /**
     * 生成测试订单数据
     * @param orderCount 单一商品订单数量
     * @param cartOrderCount 购物车订单数量
     * @param orderType 订单类型 (1:五有，2:有礼，3:新研，null:随机)
     * @return 生成结果信息
     */
    String generateTestOrderData(Integer orderCount, Integer cartOrderCount, Integer orderType);

    /**
     * 清空测试订单数据
     * @return 清空结果信息
     */
    String clearTestOrderData();

    /**
     * 创建单条测试订单
     * @param orderNo 订单号
     * @param userId 用户ID
     * @param totalFee 订单金额(分)
     * @param createTime 下单时间
     * @param type 订单类型
     * @param title 订单标题
     * @param productId 商品ID
     * @param orderParm 订单规格
     * @param remark 备注
     * @param username 用户名
     * @param phone 手机号
     * @return 创建结果信息
     */
    String createTestOrder(String orderNo, Long userId, Integer totalFee, java.time.LocalDateTime createTime,
                          Integer type, String title, Long productId, String orderParm, String remark,
                          String username, String phone);

    /**
     * 分页查询测试订单信息表数据（仅查询t_order_info_test表）
     * @param page 页码
     * @param size 每页条数
     * @param status 订单状态
     * @param type 订单类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param userName 用户名（模糊查询）
     * @param userPhone 手机号（模糊查询）
     * @return 测试订单信息列表分页数据
     */
    IPage<OrderListDTO> getTestOrderInfoList(Integer page, Integer size, String status, String type,
                                            String startTime, String endTime, String userName, String userPhone);
}
