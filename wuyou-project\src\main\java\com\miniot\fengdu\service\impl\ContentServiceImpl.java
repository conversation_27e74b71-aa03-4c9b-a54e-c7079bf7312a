package com.miniot.fengdu.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miniot.fengdu.entity.Content;
import com.miniot.fengdu.repository.ContentRepository;
import com.miniot.fengdu.service.ContentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 内容服务实现类
 */
@Slf4j
@Service
public class ContentServiceImpl extends ServiceImpl<ContentRepository, Content> implements ContentService {

    @Override
    public Page<Content> getContentPage(int page, int size, String type, Integer status,
                                       Long authorId, String title, String author, String orderBy) {
        Page<Content> contentPage = new Page<>(page, size);
        return baseMapper.findContentPage(contentPage, type, status, authorId, title, author);
    }

    @Override
    public Content getContentDetail(Long id) {
        Content content = baseMapper.selectById(id);
        if (content != null) {
            // 增加浏览量
            baseMapper.incrementViews(id);
        }
        return content;
    }

    @Override
    @Transactional
    public boolean createContent(Content content) {
        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        content.setCreatedAt(now);
        content.setUpdatedAt(now);
        
        // 管理员创建的内容默认为已发布状态
        if (content.getStatus() == null) {
            content.setStatus(1);
        }

        // 设置发布时间：已发布状态设置当前时间，其他状态设置创建时间作为占位符
        if (content.getStatus() == 1) {
            content.setPublishTime(now);
        } else {
            // 对于待审核等状态，先设置创建时间作为临时发布时间（数据库字段不允许为空）
            content.setPublishTime(now);
        }
        
        // 设置默认值
        if (content.getViews() == null) {
            content.setViews(0);
        }
        if (content.getLikes() == null) {
            content.setLikes(0);
        }
        
        return save(content);
    }

    @Override
    @Transactional
    public boolean updateContent(Content content) {
        // 更新时间
        content.setUpdatedAt(LocalDateTime.now());
        
        // 如果状态改为已发布且之前没有发布时间，则设置发布时间
        if (content.getStatus() != null && content.getStatus() == 1 && content.getPublishTime() == null) {
            content.setPublishTime(LocalDateTime.now());
        }
        
        return updateById(content);
    }

    @Override
    @Transactional
    public boolean deleteContent(Long id) {
        return removeById(id);
    }

    @Override
    public int countByStatus(Integer status) {
        return baseMapper.countByStatus(status);
    }

    @Override
    public int countByTypeAndStatus(String type, Integer status) {
        return baseMapper.countByTypeAndStatus(type, status);
    }

    @Override
    public boolean incrementViews(Long id) {
        try {
            int result = baseMapper.incrementViews(id);
            return result > 0;
        } catch (Exception e) {
            log.error("增加浏览量失败，ID: {}, 错误: {}", id, e.getMessage());
            return false;
        }
    }
}
