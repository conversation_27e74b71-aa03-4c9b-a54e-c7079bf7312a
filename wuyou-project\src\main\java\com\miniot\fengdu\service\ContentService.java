package com.miniot.fengdu.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miniot.fengdu.entity.Content;

/**
 * 内容服务接口
 */
public interface ContentService extends IService<Content> {
    
    /**
     * 分页查询内容列表
     * @param page 页码
     * @param size 每页大小
     * @param type 内容类型
     * @param status 状态
     * @param authorId 作者ID
     * @param title 标题关键词
     * @param author 作者关键词
     * @param orderBy 排序字段
     * @return 分页后的内容列表
     */
    Page<Content> getContentPage(int page, int size, String type, Integer status, 
                                Long authorId, String title, String author, String orderBy);
    
    /**
     * 获取内容详情
     * @param id 内容ID
     * @return 内容详情
     */
    Content getContentDetail(Long id);
    
    /**
     * 创建内容（管理员发布）
     * @param content 内容信息
     * @return 创建结果
     */
    boolean createContent(Content content);
    
    /**
     * 更新内容
     * @param content 内容信息
     * @return 更新结果
     */
    boolean updateContent(Content content);
    
    /**
     * 删除内容
     * @param id 内容ID
     * @return 删除结果
     */
    boolean deleteContent(Long id);
    
    /**
     * 根据状态统计内容数量
     * @param status 状态
     * @return 数量
     */
    int countByStatus(Integer status);

    /**
     * 根据类型和状态统计内容数量
     * @param type 类型
     * @param status 状态
     * @return 数量
     */
    int countByTypeAndStatus(String type, Integer status);

    /**
     * 增加浏览量
     * @param id 内容ID
     * @return 操作结果
     */
    boolean incrementViews(Long id);
}
