spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  datasource:
    url: ********************************************************************************************************************
#    url: jdbc:mysql://*************:3063/wy_mini_goods?useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
    username: root
    password: mikel<PERSON>@2025
    driver-class-name: com.mysql.cj.jdbc.Driver
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher # Springfox 3.0.0 需要此配置
  # MyBatis-Plus 配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 配置扫描的包路径
  mapper-locations: classpath:/mapper/**/*.xml
  type-aliases-package: com.miniot.fengdu.entity  # 你自己的实体类路径
  global-config:
    db-config:
      id-type: auto  # 主键策略，自增长
      logic-delete-value: 1  # 逻辑删除值
      logic-not-delete-value: 0  # 逻辑未删除值
      field-strategy: not_empty  # 字段策略，选择性更新时，空值不更新



server:
  port: 8989

# 小程序服务配置
miniprogram:
  base-url: http://localhost:8999  # 小程序服务端口
  cart-order-detail-path: /api/cart-order/payment-detail
  connect-timeout: 5000
  read-timeout: 10000
