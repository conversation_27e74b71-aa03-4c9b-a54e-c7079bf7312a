package com.miniot.fengdu.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.time.LocalDateTime;

/**
 * 创建测试订单请求DTO
 */
@Data
@ApiModel(value = "创建测试订单请求", description = "创建单条测试订单的请求参数")
public class CreateTestOrderRequest {

    @NotBlank(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号", example = "ORDER_20231201123456", required = true)
    private String orderNo;

    @NotNull(message = "用户ID不能为空")
    @ApiModelProperty(value = "用户ID", example = "1001", required = true)
    private Long userId;

    @NotNull(message = "订单金额不能为空")
    @Positive(message = "订单金额必须大于0")
    @ApiModelProperty(value = "订单金额(分)", example = "25000", required = true)
    private Integer totalFee;

    @ApiModelProperty(value = "下单时间", example = "2023-12-01T14:30:00")
    private LocalDateTime createTime;

    @NotNull(message = "订单类型不能为空")
    @ApiModelProperty(value = "订单类型：1-五有，2-有礼，3-新研", example = "1", required = true, allowableValues = "1,2,3")
    private Integer type;

    @ApiModelProperty(value = "订单标题", example = "有机蔬菜田地认养")
    private String title;

    @ApiModelProperty(value = "商品ID", example = "1001")
    private Long productId;

    @ApiModelProperty(value = "订单规格", example = "100平米/年")
    private String orderParm;

    @ApiModelProperty(value = "备注", example = "测试订单")
    private String remark;
}
