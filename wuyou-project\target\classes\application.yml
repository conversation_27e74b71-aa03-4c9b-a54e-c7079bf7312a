spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  datasource:
#    url: jdbc:mysql://*************:3063/wy_mini_goods?useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
    url: ********************************************************************************************************************
    username: root
    password: mikel<PERSON>@2025
    driver-class-name: com.mysql.cj.jdbc.Driver
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher # Springfox 3.0.0 需要此配置
  # Redis配置
  redis:
    host: **************
    port: 16380
    password: mikelei.2024
    database: 0
    timeout: 3000
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB
    
  # MyBatis-Plus 配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 配置扫描的包路径
  mapper-locations: classpath:/mapper/**/*.xml
  type-aliases-package: com.miniot.fengdu.entity  # 你自己的实体类路径
  global-config:
    db-config:
      id-type: auto  # 主键策略，自增长
      logic-delete-value: 1  # 逻辑删除值
      logic-not-delete-value: 0  # 逻辑未删除值
      field-strategy: not_empty  # 字段策略，选择性更新时，空值不更新

server:
  port: 9999
  
# 联通云OSS配置
oss:
  accessKey: E60CDB1EF6DA4EE79E9AB48545DACB073102
  secretKey: 0D39978EE5ED4292BD8BA1D4FD843CC64792
  endpoint: obs-cq.cucloud.cn
  bucketUrl: https://wuyou.obs-cq.cucloud.cn
  bucketName: wuyou

# 管理员API安全配置
admin:
  api:
    key: ADMIN_SECRET_2025