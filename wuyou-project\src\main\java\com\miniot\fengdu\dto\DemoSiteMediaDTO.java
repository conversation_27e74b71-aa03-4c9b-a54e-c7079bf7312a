package com.miniot.fengdu.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * 示范点媒体DTO
 */
@Data
@ApiModel(value = "示范点媒体DTO", description = "示范点媒体数据传输对象")
public class DemoSiteMediaDTO {
    
    @ApiModelProperty(value = "媒体ID", example = "1")
    private Long id;
    
    @NotNull(message = "示范点ID不能为空")
    @ApiModelProperty(value = "示范点ID", example = "1", required = true)
    private Long demoSiteId;
    
    @NotBlank(message = "媒体类型不能为空")
    @Pattern(regexp = "^(image|video)$", message = "媒体类型只能是image或video")
    @ApiModelProperty(value = "媒体类型：image-图片，video-视频", example = "image", required = true)
    private String mediaType;
    
    @NotBlank(message = "媒体URL不能为空")
    @ApiModelProperty(value = "媒体文件URL", example = "https://wuyou.obs-cq.cucloud.cn/demo-sites/2024/07/30/abc123.jpg", required = true)
    private String mediaUrl;
    
    @NotNull(message = "排序顺序不能为空")
    @ApiModelProperty(value = "排序顺序，数字越小越靠前", example = "1", required = true)
    private Integer sortOrder;
    
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;
    
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;
}
