package com.miniot.fengdu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miniot.fengdu.common.JsonUtil;
import com.miniot.fengdu.entity.Content;
import com.miniot.fengdu.entity.User;
import com.miniot.fengdu.entity.dto.*;
import com.miniot.fengdu.repository.ContentRepository;
import com.miniot.fengdu.repository.UserRepository;
import com.miniot.fengdu.service.ContentService;
import com.miniot.fengdu.utils.HtmlUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ContentServiceImpl extends ServiceImpl<ContentRepository, Content> implements ContentService {
    
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private ContentRepository contentRepository;

    /* @Override
     public Page<ContentResponse> getContentsByType(String type, int page, int size) {
         Page<Content> contentPage = new Page<>(page, size);
         Page<Content> result = baseMapper.findByType(contentPage, type);
         //判断当前用户是否关注了该文章作者
         Page<ContentResponse> responsePage = new Page<>(result.getCurrent(), result.getSize(), result.getTotal());
         responsePage.setRecords(result.getRecords().stream()
                 .map(ContentResponse::from)
                 .collect(Collectors.toList()));
         return responsePage;
     }*/
   @Override
   public Page<ContentResponse> getContentsByType(String type, int page, int size, Long userId) {
       Page<Content> contentPage = new Page<>(page, size);
       Page<ContentWithFollow> result = baseMapper.findByTypeWithFollow(contentPage, type, userId);

       Page<ContentResponse> responsePage = new Page<>(result.getCurrent(), result.getSize(), result.getTotal());
       responsePage.setRecords(result.getRecords().stream()
               .map(content -> {
                   ContentResponse response = ContentResponse.from(content);
                   response.setIsFollowing(content.getFollowed() != null && content.getFollowed() == 1);
                   return response;
               })
               .collect(Collectors.toList()));
       return responsePage;
   }
    
    @Override
    public ContentDetailResponse getContentDetail(String contentId, String userId) {
        // 获取内容信息
        Content content=null;
        if("1".equals(contentId)){
              content = baseMapper.findContentByHotId(contentId);
        }else
          content = baseMapper.findContentById(contentId);

        if (content == null) {
            return null;
        }
        
        // 增加浏览量
        baseMapper.incrementViews(contentId);
        
        // 构建响应对象
        ContentDetailResponse response = new ContentDetailResponse();
        response.setId(content.getId()+"");
        response.setTitle(content.getTitle());
        response.setContent(content.getContent());
        
        // 处理图片列表
        if (content.getImages() != null && !content.getImages().isEmpty()) {
            List<String> images = JsonUtil.toObject(content.getImages(), List.class);
            response.setImages(images);
        }
        
        response.setViewCount(content.getViews());
        response.setLikeCount(content.getLikes());
        response.setPublishTime(content.getPublishTime());
        
        // 检查用户是否已点赞
        boolean isLiked = baseMapper.checkUserLiked(contentId, userId) > 0;
        response.setIsLiked(isLiked);
        
        // 构建作者信息
        ContentDetailResponse.AuthorInfo authorInfo = new ContentDetailResponse.AuthorInfo();
        authorInfo.setId(content.getAuthorId()+"");
        authorInfo.setUsername(content.getAuthor());
        authorInfo.setNickname(content.getAuthor()); // 假设昵称和作者名相同，实际中可能需要从用户表获取
        
        // 从用户表获取更多作者信息
         User author = userRepository.selectById(content.getAuthorId());
         if (author != null) {
             authorInfo.setAvatar(author.getAvatar());
             authorInfo.setDescription(null);
         }
        

        // 检查用户是否已关注作者
        boolean isFollowing = baseMapper.checkUserFollowing(userId, content.getAuthorId()) > 0;
        authorInfo.setIsFollowing(isFollowing);
        
        response.setAuthor(authorInfo);
        
        return response;
    }

    @Override
    public Content getContentHot() {
       Content content = contentRepository.selectOne(new QueryWrapper<Content>().eq("tag", "hot").eq("status", 1).last("limit 1"));

        return content;
    }

    @Override
    public ContentPublishResponse publishContent(ContentPublishRequest request, String userId) {
        // 防止XSS攻击，过滤标题和内容
        String safeTitle = HtmlUtils.cleanHtml(request.getTitle());
        String safeContent = HtmlUtils.cleanHtml(request.getContent());
        
        // 创建内容对象
        Content content = new Content();
//        content.setId(UUID.randomUUID().toString().replace("-", ""));
        content.setTitle(safeTitle);
        content.setType("article"); // 默认为文章类型
        
        // 设置封面图，如果没有提供，则使用第一张图片作为封面
        if (StringUtils.hasText(request.getCoverImage())) {
            content.setCover(request.getCoverImage());
        } else if (request.getImages() != null && !request.getImages().isEmpty()) {
            content.setCover(request.getImages().get(0));
        } else {
            // 设置默认封面
            content.setCover("https://example.com/default-cover.jpg");
        }
        
        // 设置摘要（从内容中提取前100个字符）
        String summary = safeContent.length() > 100 ? safeContent.substring(0, 100) + "..." : safeContent;
        content.setSummary(summary);
        
        // 设置内容详情
        content.setContent(safeContent);
        
        // 处理图片列表
        if (request.getImages() != null && !request.getImages().isEmpty()) {
            content.setImages(JsonUtil.toJson(request.getImages()));
        }
        
        // 设置作者信息
        content.setAuthorId(Long.valueOf(userId));
        // 从用户服务获取作者名称，这里简单模拟
        User author = userRepository.selectById(userId);
        if(author==null)
            throw new RuntimeException("用户不存在");
        content.setAuthor(author != null ? author.getNickname() : "未知用户");
        content.setAuthorImg(author.getAvatar());
        // 设置发布时间和其他默认值
        LocalDateTime now = LocalDateTime.now();
        content.setPublishTime(now);
        content.setViews(0);
        content.setLikes(0);
        content.setStatus(2); // 2-待审核（用户发帖需要审核）
        content.setCreatedAt(now);
        content.setUpdatedAt(now);
        
        // 保存内容
        boolean success = this.save(content);
        
        if (!success) {
            throw new RuntimeException("内容发布失败");
        }
        
        // 构建并返回响应
        ContentPublishResponse response = new ContentPublishResponse();
        response.setContentId(content.getId().toString()); // 转换为字符串
        response.setTitle(content.getTitle());
        response.setPublishTime(content.getPublishTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        response.setStatus("published");
        
        return response;
    }
} 