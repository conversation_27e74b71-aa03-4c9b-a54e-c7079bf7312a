package com.starlight.paymentdemo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 研学活动规格实体类（支付服务）
 */
@Data
@TableName("research_activity_model")
@ApiModel(value = "研学活动规格实体", description = "研学活动规格信息")
public class ResearchActivityModel {
    
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "规格ID", example = "47")
    private Long id;
    
    @TableField("parent_activity_id")
    @ApiModelProperty(value = "父活动ID", example = "28")
    private Long parentActivityId;
    
    @TableField("title")
    @ApiModelProperty(value = "规格标题", example = "稻田农耕文化体验-成人票")
    private String title;
    
    @TableField("price")
    @ApiModelProperty(value = "价格", example = "68.00")
    private BigDecimal price;
    
    @TableField("price_unit")
    @ApiModelProperty(value = "价格单位", example = "元/人")
    private String priceUnit;
    
    @TableField("status")
    @ApiModelProperty(value = "状态：0-下架，1-上架", example = "1")
    private Integer status;
    
    @TableField("created_at")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;
    
    @TableField("updated_at")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;
}
