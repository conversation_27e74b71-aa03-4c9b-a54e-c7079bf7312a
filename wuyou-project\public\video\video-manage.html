<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <style>
        body {
            padding: 20px;
        }
        .card {
            margin-bottom: 20px;
        }
        .progress {
            height: 20px;
            margin-bottom: 10px;
        }
        .video-preview {
            max-width: 100%;
            margin-top: 10px;
        }
        .bucket-list {
            max-height: 200px;
            overflow-y: auto;
        }
        .nav-tabs {
            margin-bottom: 20px;
        }
        .tab-content {
            padding: 20px;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 .25rem .25rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">视频管理系统</h1>
        
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="upload-tab" data-toggle="tab" href="#upload" role="tab">视频上传</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="presigned-tab" data-toggle="tab" href="#presigned" role="tab">预签名上传</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="bucket-tab" data-toggle="tab" href="#bucket" role="tab">Bucket管理</a>
            </li>
        </ul>
        
        <div class="tab-content" id="myTabContent">
            <!-- 视频上传标签页 -->
            <div class="tab-pane fade show active" id="upload" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5>上传视频</h5>
                    </div>
                    <div class="card-body">
                        <form id="uploadForm">
                            <div class="form-group">
                                <label for="videoFile">选择视频文件</label>
                                <input type="file" class="form-control-file" id="videoFile" accept="video/*">
                            </div>
                            <div class="form-group">
                                <label for="directory">存储目录</label>
                                <input type="text" class="form-control" id="directory" value="videos">
                            </div>
                            <div class="progress">
                                <div class="progress-bar" id="uploadProgress" role="progressbar" style="width: 0%"></div>
                            </div>
                            <button type="button" class="btn btn-primary" onclick="uploadVideo()">上传</button>
                        </form>
                        
                        <div id="uploadResult" class="mt-3" style="display: none;">
                            <h5>上传结果</h5>
                            <pre id="uploadResultContent" class="border p-2 bg-light"></pre>
                            <button type="button" class="btn btn-danger" onclick="deleteVideo()">删除视频</button>
                        </div>
                        
                        <video id="videoPreview" class="video-preview" controls style="display: none;"></video>
                    </div>
                </div>
            </div>
            
            <!-- 预签名上传标签页 -->
            <div class="tab-pane fade" id="presigned" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5>预签名URL上传</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="objectKey">对象键</label>
                            <input type="text" class="form-control" id="objectKey" value="videos/test.mp4">
                        </div>
                        <div class="form-group">
                            <label for="expireTime">过期时间(秒)</label>
                            <input type="number" class="form-control" id="expireTime" value="3600">
                        </div>
                        <button type="button" class="btn btn-primary" onclick="getPresignedUrl()">获取预签名URL</button>
                        
                        <div id="presignedResult" class="mt-3" style="display: none;">
                            <h5>预签名URL</h5>
                            <pre id="presignedUrlContent" class="border p-2 bg-light"></pre>
                        </div>
                        
                        <hr>
                        
                        <div class="form-group">
                            <label for="presignedFile">选择要上传的文件</label>
                            <input type="file" class="form-control-file" id="presignedFile" accept="video/*">
                        </div>
                        <div class="progress">
                            <div class="progress-bar" id="presignedProgress" role="progressbar" style="width: 0%"></div>
                        </div>
                        <button type="button" class="btn btn-primary" id="uploadPresignedBtn" onclick="uploadWithPresignedUrl()" disabled>使用预签名URL上传</button>
                        
                        <div id="presignedUploadResult" class="mt-3" style="display: none;">
                            <h5>上传结果</h5>
                            <pre id="presignedUploadResultContent" class="border p-2 bg-light"></pre>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Bucket管理标签页 -->
            <div class="tab-pane fade" id="bucket" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5>Bucket管理</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="bucketName">Bucket名称</label>
                            <input type="text" class="form-control" id="bucketName" placeholder="输入要创建的Bucket名称">
                        </div>
                        <button type="button" class="btn btn-primary" onclick="createBucket()">创建Bucket</button>
                        
                        <hr>
                        
                        <h5>Bucket列表</h5>
                        <button type="button" class="btn btn-secondary mb-2" onclick="listBuckets()">刷新列表</button>
                        <ul class="list-group bucket-list" id="bucketList">
                            <li class="list-group-item">加载中...</li>
                        </ul>
                        
                        <div id="bucketResult" class="mt-3" style="display: none;">
                            <h5>操作结果</h5>
                            <pre id="bucketResultContent" class="border p-2 bg-light"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let uploadedObjectKey = '';
        let presignedUrl = '';
        const API_BASE_URL = '/back/api/admin/video';
        
        // 页面加载完成后执行
        $(document).ready(function() {
            // 初始化获取Bucket列表
            listBuckets();
        });
        
        // 上传视频
        function uploadVideo() {
            const fileInput = document.getElementById('videoFile');
            const directory = document.getElementById('directory').value || 'videos';
            const uploadProgress = document.getElementById('uploadProgress');
            const uploadResult = document.getElementById('uploadResult');
            const uploadResultContent = document.getElementById('uploadResultContent');
            const videoPreview = document.getElementById('videoPreview');
            
            if (!fileInput.files.length) {
                alert('请选择视频文件');
                return;
            }
            
            const file = fileInput.files[0];
            const formData = new FormData();
            formData.append('file', file);
            formData.append('directory', directory);
            
            // 重置进度条和结果
            uploadProgress.style.width = '0%';
            uploadResult.style.display = 'none';
            videoPreview.style.display = 'none';
            
            $.ajax({
                url: API_BASE_URL + '/upload',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function() {
                    const xhr = new XMLHttpRequest();
                    xhr.upload.addEventListener('progress', function(e) {
                        if (e.lengthComputable) {
                            const percent = Math.round((e.loaded / e.total) * 100);
                            uploadProgress.style.width = percent + '%';
                            uploadProgress.textContent = percent + '%';
                        }
                    });
                    return xhr;
                },
                success: function(response) {
                    uploadResultContent.textContent = JSON.stringify(response, null, 2);
                    uploadResult.style.display = 'block';
                    
                    if (response.code === 1 && response.data && response.data.url) {
                        videoPreview.src = response.data.url;
                        videoPreview.style.display = 'block';
                        uploadedObjectKey = response.data.objectKey;
                    }
                },
                error: function(xhr) {
                    alert('上传失败: ' + xhr.statusText);
                }
            });
        }
        
        // 删除视频
        function deleteVideo() {
            if (!uploadedObjectKey) {
                alert('没有可删除的视频');
                return;
            }
            
            $.ajax({
                url: API_BASE_URL + '/delete',
                type: 'DELETE',
                data: { objectKey: uploadedObjectKey },
                success: function(response) {
                    alert(response.msg);
                    if (response.code === 1) {
                        document.getElementById('uploadResult').style.display = 'none';
                        document.getElementById('videoPreview').style.display = 'none';
                        uploadedObjectKey = '';
                    }
                },
                error: function(xhr) {
                    alert('删除失败: ' + xhr.statusText);
                }
            });
        }
        
        // 获取预签名URL
        function getPresignedUrl() {
            const objectKey = document.getElementById('objectKey').value;
            const expireTime = document.getElementById('expireTime').value;
            const presignedResult = document.getElementById('presignedResult');
            const presignedUrlContent = document.getElementById('presignedUrlContent');
            
            if (!objectKey) {
                alert('请输入对象键');
                return;
            }
            
            $.ajax({
                url: API_BASE_URL + '/presigned-url',
                type: 'GET',
                data: { 
                    objectKey: objectKey,
                    expireTime: expireTime
                },
                success: function(response) {
                    presignedUrlContent.textContent = response.data;
                    presignedResult.style.display = 'block';
                    
                    if (response.code === 1 && response.data) {
                        presignedUrl = response.data;
                        document.getElementById('uploadPresignedBtn').disabled = false;
                    }
                },
                error: function(xhr) {
                    alert('获取预签名URL失败: ' + xhr.statusText);
                }
            });
        }
        
        // 使用预签名URL上传
        function uploadWithPresignedUrl() {
            const fileInput = document.getElementById('presignedFile');
            const presignedProgress = document.getElementById('presignedProgress');
            const presignedUploadResult = document.getElementById('presignedUploadResult');
            const presignedUploadResultContent = document.getElementById('presignedUploadResultContent');
            
            if (!fileInput.files.length) {
                alert('请选择视频文件');
                return;
            }
            
            if (!presignedUrl) {
                alert('请先获取预签名URL');
                return;
            }
            
            const file = fileInput.files[0];
            
            // 重置进度条和结果
            presignedProgress.style.width = '0%';
            presignedUploadResult.style.display = 'none';
            
            const xhr = new XMLHttpRequest();
            
            // 监听上传进度
            xhr.upload.addEventListener('progress', function(event) {
                if (event.lengthComputable) {
                    const percentComplete = Math.round((event.loaded / event.total) * 100);
                    presignedProgress.style.width = percentComplete + '%';
                    presignedProgress.textContent = percentComplete + '%';
                }
            });
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    presignedUploadResult.style.display = 'block';
                    
                    if (xhr.status === 200) {
                        presignedUploadResultContent.textContent = '上传成功！\n\nHTTP状态码: ' + xhr.status;
                    } else {
                        presignedUploadResultContent.textContent = '上传失败！\n\nHTTP状态码: ' + xhr.status + '\n错误: ' + xhr.statusText;
                    }
                }
            };
            
            xhr.open('PUT', presignedUrl, true);
            xhr.setRequestHeader('Content-Type', file.type);
            xhr.send(file);
        }
        
        // 创建Bucket
        function createBucket() {
            const bucketName = document.getElementById('bucketName').value;
            const bucketResult = document.getElementById('bucketResult');
            const bucketResultContent = document.getElementById('bucketResultContent');
            
            if (!bucketName) {
                alert('请输入Bucket名称');
                return;
            }
            
            $.ajax({
                url: API_BASE_URL + '/bucket/create',
                type: 'POST',
                data: { bucketName: bucketName },
                success: function(response) {
                    bucketResultContent.textContent = JSON.stringify(response, null, 2);
                    bucketResult.style.display = 'block';
                    
                    if (response.code === 1) {
                        alert('创建Bucket成功');
                        listBuckets(); // 刷新列表
                    } else {
                        alert('创建Bucket失败: ' + response.msg);
                    }
                },
                error: function(xhr) {
                    alert('创建Bucket失败: ' + xhr.statusText);
                }
            });
        }
        
        // 获取Bucket列表
        function listBuckets() {
            const bucketList = document.getElementById('bucketList');
            bucketList.innerHTML = '<li class="list-group-item">加载中...</li>';
            
            $.ajax({
                url: API_BASE_URL + '/bucket/list',
                type: 'GET',
                success: function(response) {
                    if (response.code === 1 && response.data) {
                        if (response.data.length > 0) {
                            bucketList.innerHTML = '';
                            response.data.forEach(function(bucket) {
                                bucketList.innerHTML += `<li class="list-group-item">${bucket}</li>`;
                            });
                        } else {
                            bucketList.innerHTML = '<li class="list-group-item">没有可用的Bucket</li>';
                        }
                    } else {
                        bucketList.innerHTML = '<li class="list-group-item">获取Bucket列表失败</li>';
                    }
                },
                error: function(xhr) {
                    bucketList.innerHTML = '<li class="list-group-item">获取Bucket列表失败: ' + xhr.statusText + '</li>';
                }
            });
        }
    </script>
</body>
</html> 