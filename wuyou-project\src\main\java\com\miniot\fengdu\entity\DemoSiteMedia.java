package com.miniot.fengdu.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 示范点媒体实体
 */
@Data
@TableName("wy_demo_site_media")
@ApiModel(value = "示范点媒体实体", description = "示范点媒体文件信息")
public class DemoSiteMedia {
    
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "媒体ID", example = "1")
    private Long id;
    
    @TableField("demo_site_id")
    @ApiModelProperty(value = "示范点ID", example = "1", required = true)
    private Long demoSiteId;
    
    @TableField("media_type")
    @ApiModelProperty(value = "媒体类型：image-图片，video-视频", example = "image", required = true)
    private String mediaType;
    
    @TableField("media_url")
    @ApiModelProperty(value = "媒体文件URL", example = "https://wuyou.obs-cq.cucloud.cn/demo-sites/2024/07/30/abc123.jpg", required = true)
    private String mediaUrl;
    
    @TableField("sort_order")
    @ApiModelProperty(value = "排序顺序，数字越小越靠前", example = "1", required = true)
    private Integer sortOrder;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;
    
    /**
     * 媒体类型枚举
     */
    public static class MediaType {
        public static final String IMAGE = "image";
        public static final String VIDEO = "video";
    }
}
