package com.miniot.fengdu.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.miniot.fengdu.entity.DemoSiteMedia;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 示范点媒体Repository
 */
@Mapper
public interface DemoSiteMediaRepository extends BaseMapper<DemoSiteMedia> {
    
    /**
     * 根据示范点ID获取媒体列表（按排序顺序）
     */
    @Select("SELECT * FROM wy_demo_site_media WHERE demo_site_id = #{demoSiteId} ORDER BY sort_order ASC, id ASC")
    List<DemoSiteMedia> findByDemoSiteIdOrderBySortOrder(@Param("demoSiteId") Long demoSiteId);
    
    /**
     * 根据示范点ID和媒体类型获取媒体列表
     */
    @Select("SELECT * FROM wy_demo_site_media WHERE demo_site_id = #{demoSiteId} AND media_type = #{mediaType} ORDER BY sort_order ASC, id ASC")
    List<DemoSiteMedia> findByDemoSiteIdAndMediaType(@Param("demoSiteId") Long demoSiteId, @Param("mediaType") String mediaType);
    
    /**
     * 获取示范点媒体数量统计
     */
    @Select("SELECT media_type, COUNT(*) as count FROM wy_demo_site_media WHERE demo_site_id = #{demoSiteId} GROUP BY media_type")
    List<MediaTypeCount> getMediaTypeCount(@Param("demoSiteId") Long demoSiteId);
    
    /**
     * 获取示范点下一个排序号
     */
    @Select("SELECT COALESCE(MAX(sort_order), 0) + 1 FROM wy_demo_site_media WHERE demo_site_id = #{demoSiteId}")
    Integer getNextSortOrder(@Param("demoSiteId") Long demoSiteId);
    
    /**
     * 批量更新排序顺序
     */
    @Update("UPDATE wy_demo_site_media SET sort_order = #{sortOrder} WHERE id = #{id}")
    int updateSortOrder(@Param("id") Long id, @Param("sortOrder") Integer sortOrder);
    
    /**
     * 媒体类型统计内部类
     */
    class MediaTypeCount {
        private String mediaType;
        private Integer count;
        
        public String getMediaType() {
            return mediaType;
        }
        
        public void setMediaType(String mediaType) {
            this.mediaType = mediaType;
        }
        
        public Integer getCount() {
            return count;
        }
        
        public void setCount(Integer count) {
            this.count = count;
        }
    }
}
