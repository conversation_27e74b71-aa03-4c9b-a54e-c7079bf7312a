package com.miniot.fengdu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 内容实体类
 */
@Data
@TableName("content")
@ApiModel(value = "内容实体", description = "内容信息")
public class Content {
    
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "内容ID", example = "5001")
    private Long id;
    
    @TableField("title")
    @ApiModelProperty(value = "内容标题", example = "有机农业的未来发展趋势")
    private String title;
    
    @TableField("type")
    @ApiModelProperty(value = "内容类型：article-文章，video-视频，topic-话题", example = "article")
    private String type;
    
    @TableField("cover")
    @ApiModelProperty(value = "封面图URL", example = "https://example.com/content/organic_farming.jpg")
    private String cover;
    
    @TableField("summary")
    @ApiModelProperty(value = "内容摘要", example = "本文探讨了有机农业在未来十年的发展趋势...")
    private String summary;
    
    @TableField("content")
    @ApiModelProperty(value = "内容详情")
    private String content;
    
    @TableField("images")
    @ApiModelProperty(value = "图片列表，JSON格式存储多张图片URL")
    private String images;
    
    @TableField("author")
    @ApiModelProperty(value = "作者", example = "李农夫")
    private String author;
    
    @TableField("author_id")
    @ApiModelProperty(value = "作者ID，关联用户表", example = "1002")
    private Long authorId;
    
    @TableField("author_img")
    @ApiModelProperty(value = "作者头像", example = "https://example.com/avatar.jpg")
    private String authorImg;
    
    @TableField("publish_time")
    @ApiModelProperty(value = "发布时间")
    private LocalDateTime publishTime;
    
    @TableField("views")
    @ApiModelProperty(value = "浏览量", example = "1560")
    private Integer views;
    
    @TableField("likes")
    @ApiModelProperty(value = "点赞数", example = "320")
    private Integer likes;
    
    @TableField("status")
    @ApiModelProperty(value = "状态：0-未发布，1-已发布，2-待审核，3-审核拒绝", example = "1")
    private Integer status;
    
    @TableField("tag")
    @ApiModelProperty(value = "标签", example = "有机农业,绿色种植")
    private String tag;
    
    @TableField("created_at")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;
    
    @TableField("updated_at")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;
}
