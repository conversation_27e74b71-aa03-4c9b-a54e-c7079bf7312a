<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小程序支付测试</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #07c160;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .section:last-child {
            border-bottom: none;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        input, select, button {
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
            width: 100%;
            box-sizing: border-box;
            margin-bottom: 15px;
        }
        button {
            background-color: #07c160;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #06ad56;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border-left: 4px solid #07c160;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .error {
            border-left-color: #ff4d4f;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>微信小程序支付测试</h1>
        
        <div class="section">
            <h2>1. 发起支付</h2>
            <label for="productId">选择商品</label>
            <select id="productId">
                <option value="1">商品1</option>
                <option value="2">商品2</option>
            </select>
            
            <label for="openid">用户OpenID</label>
            <input type="text" id="openid" placeholder="输入用户的OpenID" value="oWmnN5PsRD-FVYbLYxQznP_j7fPU">
            
            <button onclick="createPayment()">发起支付</button>
            
            <div id="paymentResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="section">
            <h2>2. 查询订单</h2>
            <label for="queryOrderNo">订单号</label>
            <input type="text" id="queryOrderNo" placeholder="输入订单号">
            
            <button onclick="queryOrder()">查询订单</button>
            
            <div id="queryResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="section">
            <h2>3. 关闭订单</h2>
            <label for="closeOrderNo">订单号</label>
            <input type="text" id="closeOrderNo" placeholder="输入订单号">
            
            <button onclick="closeOrder()">关闭订单</button>
            
            <div id="closeResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="section">
            <h2>4. 申请退款</h2>
            <label for="refundOrderNo">订单号</label>
            <input type="text" id="refundOrderNo" placeholder="输入订单号">
            
            <label for="refundReason">退款原因</label>
            <input type="text" id="refundReason" placeholder="输入退款原因" value="商品不满意">
            
            <button onclick="refundOrder()">申请退款</button>
            
            <div id="refundResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="section">
            <h2>5. 查询退款</h2>
            <label for="queryRefundOrderNo">订单号</label>
            <input type="text" id="queryRefundOrderNo" placeholder="输入订单号">
            
            <button onclick="queryRefund()">查询退款</button>
            
            <div id="queryRefundResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="section">
            <h2>获取OpenID</h2>
            <label for="code">授权码(code)</label>
            <input type="text" id="code" placeholder="输入小程序授权码">
            <button onclick="getOpenId()">获取OpenID</button>
            <div id="openidResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 保存当前订单号
        let currentOrderNo = '';
        
        // 发起支付
        async function createPayment() {
            const productId = document.getElementById('productId').value;
            const openid = document.getElementById('openid').value;
            const resultDiv = document.getElementById('paymentResult');
            
            if (!openid) {
                showResult(resultDiv, '请输入用户OpenID', true);
                return;
            }
            
            try {
                resultDiv.innerHTML = '请求中...';
                resultDiv.style.display = 'block';
                
                const response = await fetch(`/api/wx-pay-v2/jsapi/${productId}?openid=${openid}`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.code === 20000) {
                    currentOrderNo = result.data.orderNo;
                    document.getElementById('queryOrderNo').value = currentOrderNo;
                    document.getElementById('closeOrderNo').value = currentOrderNo;
                    document.getElementById('refundOrderNo').value = currentOrderNo;
                    document.getElementById('queryRefundOrderNo').value = currentOrderNo;
                    
                    showResult(resultDiv, '支付参数获取成功：\n' + JSON.stringify(result.data, null, 2));
                    
                    // 在实际小程序中，这里会调用wx.requestPayment
                    const mockPaymentResult = `
在实际小程序中，这里会调用:

wx.requestPayment({
  timeStamp: '${result.data.jsapiParams.timeStamp}',
  nonceStr: '${result.data.jsapiParams.nonceStr}',
  package: '${result.data.jsapiParams.package}',
  signType: '${result.data.jsapiParams.signType}',
  paySign: '${result.data.jsapiParams.paySign}',
  success (res) { 
    console.log('支付成功', res)
  },
  fail (res) { 
    console.log('支付失败', res)
  }
})`;
                    
                    showResult(resultDiv, mockPaymentResult);
                } else {
                    showResult(resultDiv, '请求失败：' + result.message, true);
                }
            } catch (error) {
                showResult(resultDiv, '请求异常：' + error.message, true);
            }
        }
        
        // 查询订单
        async function queryOrder() {
            const orderNo = document.getElementById('queryOrderNo').value;
            const resultDiv = document.getElementById('queryResult');
            
            if (!orderNo) {
                showResult(resultDiv, '请输入订单号', true);
                return;
            }
            
            try {
                resultDiv.innerHTML = '请求中...';
                resultDiv.style.display = 'block';
                
                const response = await fetch(`/api/wx-pay-v2/query/${orderNo}`);
                const result = await response.json();
                
                showResult(resultDiv, JSON.stringify(result, null, 2));
            } catch (error) {
                showResult(resultDiv, '请求异常：' + error.message, true);
            }
        }
        
        // 关闭订单
        async function closeOrder() {
            const orderNo = document.getElementById('closeOrderNo').value;
            const resultDiv = document.getElementById('closeResult');
            
            if (!orderNo) {
                showResult(resultDiv, '请输入订单号', true);
                return;
            }
            
            try {
                resultDiv.innerHTML = '请求中...';
                resultDiv.style.display = 'block';
                
                const response = await fetch(`/api/wx-pay-v2/close/${orderNo}`, {
                    method: 'POST'
                });
                const result = await response.json();
                
                showResult(resultDiv, JSON.stringify(result, null, 2));
            } catch (error) {
                showResult(resultDiv, '请求异常：' + error.message, true);
            }
        }
        
        // 申请退款
        async function refundOrder() {
            const orderNo = document.getElementById('refundOrderNo').value;
            const reason = document.getElementById('refundReason').value;
            const resultDiv = document.getElementById('refundResult');
            
            if (!orderNo) {
                showResult(resultDiv, '请输入订单号', true);
                return;
            }
            
            if (!reason) {
                showResult(resultDiv, '请输入退款原因', true);
                return;
            }
            
            try {
                resultDiv.innerHTML = '请求中...';
                resultDiv.style.display = 'block';
                
                const response = await fetch(`/api/wx-pay-v2/refund/${orderNo}/${encodeURIComponent(reason)}`, {
                    method: 'POST'
                });
                const result = await response.json();
                
                showResult(resultDiv, JSON.stringify(result, null, 2));
            } catch (error) {
                showResult(resultDiv, '请求异常：' + error.message, true);
            }
        }
        
        // 查询退款
        async function queryRefund() {
            const orderNo = document.getElementById('queryRefundOrderNo').value;
            const resultDiv = document.getElementById('queryRefundResult');
            
            if (!orderNo) {
                showResult(resultDiv, '请输入订单号', true);
                return;
            }
            
            try {
                resultDiv.innerHTML = '请求中...';
                resultDiv.style.display = 'block';
                
                const response = await fetch(`/api/wx-pay-v2/refund/query/${orderNo}`);
                const result = await response.json();
                
                showResult(resultDiv, JSON.stringify(result, null, 2));
            } catch (error) {
                showResult(resultDiv, '请求异常：' + error.message, true);
            }
        }
        
        // 获取OpenID
        async function getOpenId() {
            const code = document.getElementById('code').value;
            const resultDiv = document.getElementById('openidResult');
            
            if (!code) {
                showResult(resultDiv, '请输入授权码', true);
                return;
            }
            
            try {
                resultDiv.innerHTML = '请求中...';
                resultDiv.style.display = 'block';
                
                const response = await fetch(`/api/wx-login/${code}`);
                const result = await response.json();
                
                if (result.code === 0) {
                    const openid = result.data.openid;
                    document.getElementById('openid').value = openid;
                    showResult(resultDiv, `获取成功，OpenID: ${openid}`);
                } else {
                    showResult(resultDiv, `获取失败: ${result.message}`, true);
                }
            } catch (error) {
                showResult(resultDiv, '请求异常：' + error.message, true);
            }
        }
        
        // 显示结果
        function showResult(element, message, isError = false) {
            element.textContent = message;
            element.style.display = 'block';
            
            if (isError) {
                element.classList.add('error');
            } else {
                element.classList.remove('error');
            }
        }
    </script>
</body>
</html> 