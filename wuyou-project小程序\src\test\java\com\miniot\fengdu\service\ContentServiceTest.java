package com.miniot.fengdu.service;

import com.miniot.fengdu.entity.Content;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 内容服务测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class ContentServiceTest {

    @Test
    public void testCreateContentWithPendingStatus() {
        // 创建测试内容对象
        Content content = new Content();
        content.setTitle("测试文章");
        content.setType("article");
        content.setCover("test-cover.jpg");
        content.setSummary("这是一篇测试文章");
        content.setContent("测试文章内容");
        content.setAuthor("测试用户");
        content.setAuthorId(1L);
        content.setAuthorImg("test-avatar.jpg");
        
        // 模拟用户发帖时的状态设置
        LocalDateTime now = LocalDateTime.now();
        content.setPublishTime(now);
        content.setViews(0);
        content.setLikes(0);
        content.setStatus(2); // 2-待审核（用户发帖需要审核）
        content.setCreatedAt(now);
        content.setUpdatedAt(now);
        
        // 验证状态设置正确
        assertEquals(2, content.getStatus(), "用户发帖状态应该设置为待审核(2)");
        assertNotNull(content.getPublishTime(), "发布时间不能为空");
        assertEquals(0, content.getViews(), "初始浏览量应为0");
        assertEquals(0, content.getLikes(), "初始点赞数应为0");
    }
}
