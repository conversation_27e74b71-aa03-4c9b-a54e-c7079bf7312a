package com.miniot.fengdu.test;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miniot.fengdu.entity.dto.UnifiedOrderListResponse;
import com.miniot.fengdu.service.UnifiedOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 🚀 统一订单性能测试接口
 *
 * 优化策略说明：
 * 1. 保持原有的合并查询功能：同时查询购物车订单和单一商品订单，按时间统一排序
 * 2. 并行查询优化：使用多线程同时查询两种订单类型，减少总查询时间
 * 3. SQL查询优化：在数据库层面进行时间过滤，减少数据传输量
 * 4. 限制查询数量：避免使用 Integer.MAX_VALUE，防止内存溢出
 * 5. 数据库索引优化：添加复合索引支持高效查询
 */
@Slf4j
@RestController
@RequestMapping("/test/unified-order")
public class UnifiedOrderPerformanceTest {
    
    @Autowired
    private UnifiedOrderService unifiedOrderService;
    
    /**
     * 性能测试接口
     */
    @GetMapping("/performance-test")
    public Object performanceTest(
            @RequestParam Long userId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String orderType,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        log.info("🚀 开始性能测试");
        log.info("测试参数: userId={}, status={}, orderType={}, startTime={}, endTime={}, page={}, size={}",
                userId, status, orderType, startTime, endTime, page, size);
        
        long startTimeMs = System.currentTimeMillis();
        
        try {
            Page<UnifiedOrderListResponse> result = unifiedOrderService.getUnifiedOrderList(
                    userId, status, orderType, startTime, endTime, page, size);
            
            long endTimeMs = System.currentTimeMillis();
            long duration = endTimeMs - startTimeMs;
            
            log.info("🚀 性能测试完成");
            log.info("总耗时: {}ms", duration);
            log.info("查询结果: 总数={}, 当前页记录数={}", result.getTotal(), result.getRecords().size());
            
            // 返回性能测试结果
            return new PerformanceTestResult(
                    duration,
                    result.getTotal(),
                    result.getRecords().size(),
                    result.getCurrent(),
                    result.getSize(),
                    result.getPages(),
                    "优化版本查询成功"
            );
            
        } catch (Exception e) {
            long endTimeMs = System.currentTimeMillis();
            long duration = endTimeMs - startTimeMs;
            
            log.error("🚀 性能测试失败，耗时: {}ms", duration, e);
            
            return new PerformanceTestResult(
                    duration,
                    0L,
                    0,
                    (long) page,
                    (long) size,
                    0L,
                    "查询失败: " + e.getMessage()
            );
        }
    }
    
    /**
     * 性能测试结果
     */
    public static class PerformanceTestResult {
        private long durationMs;
        private long totalRecords;
        private int currentPageRecords;
        private long currentPage;
        private long pageSize;
        private long totalPages;
        private String message;
        
        public PerformanceTestResult(long durationMs, long totalRecords, int currentPageRecords, 
                                   long currentPage, long pageSize, long totalPages, String message) {
            this.durationMs = durationMs;
            this.totalRecords = totalRecords;
            this.currentPageRecords = currentPageRecords;
            this.currentPage = currentPage;
            this.pageSize = pageSize;
            this.totalPages = totalPages;
            this.message = message;
        }
        
        // Getters
        public long getDurationMs() { return durationMs; }
        public long getTotalRecords() { return totalRecords; }
        public int getCurrentPageRecords() { return currentPageRecords; }
        public long getCurrentPage() { return currentPage; }
        public long getPageSize() { return pageSize; }
        public long getTotalPages() { return totalPages; }
        public String getMessage() { return message; }
        
        // 性能评级
        public String getPerformanceRating() {
            if (durationMs < 500) return "优秀 (< 0.5s)";
            if (durationMs < 1000) return "良好 (< 1s)";
            if (durationMs < 2000) return "一般 (< 2s)";
            if (durationMs < 5000) return "较差 (< 5s)";
            return "很差 (>= 5s)";
        }
        
        @Override
        public String toString() {
            return String.format(
                "PerformanceTestResult{耗时=%dms, 总记录数=%d, 当前页记录数=%d, 当前页=%d, 每页大小=%d, 总页数=%d, 性能评级='%s', 消息='%s'}",
                durationMs, totalRecords, currentPageRecords, currentPage, pageSize, totalPages, getPerformanceRating(), message
            );
        }
    }
    
    /**
     * 批量性能测试
     */
    @GetMapping("/batch-test")
    public Object batchTest(@RequestParam Long userId) {
        log.info("🚀 开始批量性能测试，用户ID: {}", userId);
        
        // 测试场景
        Object[] testCases = {
            new Object[]{"所有订单", null, null, null, null, 1, 10},
            new Object[]{"购物车订单", null, "购物车订单", null, null, 1, 10},
            new Object[]{"有礼订单", null, "有礼", null, null, 1, 10},
            new Object[]{"五有订单", null, "五有", null, null, 1, 10},
            new Object[]{"未支付订单", "未支付", null, null, null, 1, 10},
            new Object[]{"已支付订单", "支付成功", null, null, null, 1, 10},
            new Object[]{"大页面查询", null, null, null, null, 1, 50},
        };
        
        StringBuilder results = new StringBuilder("🚀 批量性能测试结果:\n\n");
        
        for (Object[] testCase : testCases) {
            String testName = (String) testCase[0];
            String status = (String) testCase[1];
            String orderType = (String) testCase[2];
            String startTime = (String) testCase[3];
            String endTime = (String) testCase[4];
            int page = (int) testCase[5];
            int size = (int) testCase[6];
            
            try {
                long startTimeMs = System.currentTimeMillis();
                Page<UnifiedOrderListResponse> result = unifiedOrderService.getUnifiedOrderList(
                        userId, status, orderType, startTime, endTime, page, size);
                long duration = System.currentTimeMillis() - startTimeMs;
                
                results.append(String.format("✅ %s: %dms (总数: %d, 返回: %d)\n", 
                        testName, duration, result.getTotal(), result.getRecords().size()));
                
            } catch (Exception e) {
                results.append(String.format("❌ %s: 失败 - %s\n", testName, e.getMessage()));
            }
        }
        
        log.info(results.toString());
        return results.toString();
    }
}
