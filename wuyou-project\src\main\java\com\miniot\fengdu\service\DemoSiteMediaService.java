package com.miniot.fengdu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miniot.fengdu.dto.DemoSiteMediaDTO;
import com.miniot.fengdu.dto.DemoSiteMediaListDTO;
import com.miniot.fengdu.entity.DemoSiteMedia;

import java.util.List;

/**
 * 示范点媒体服务接口
 */
public interface DemoSiteMediaService extends IService<DemoSiteMedia> {
    
    /**
     * 获取示范点媒体列表
     *
     * @param demoSiteId 示范点ID
     * @return 媒体列表DTO
     */
    DemoSiteMediaListDTO getDemoSiteMediaList(Long demoSiteId);
    
    /**
     * 根据类型获取示范点媒体列表
     *
     * @param demoSiteId 示范点ID
     * @param mediaType 媒体类型
     * @return 媒体列表
     */
    List<DemoSiteMediaDTO> getDemoSiteMediaByType(Long demoSiteId, String mediaType);
    
    /**
     * 添加媒体
     *
     * @param demoSiteMediaDTO 媒体DTO
     * @return 新添加的媒体ID
     */
    Long addMedia(DemoSiteMediaDTO demoSiteMediaDTO);
    
    /**
     * 更新媒体信息
     *
     * @param id 媒体ID
     * @param demoSiteMediaDTO 媒体DTO
     * @return 更新是否成功
     */
    boolean updateMedia(Long id, DemoSiteMediaDTO demoSiteMediaDTO);
    
    /**
     * 删除媒体
     *
     * @param id 媒体ID
     * @return 删除是否成功
     */
    boolean deleteMedia(Long id);
    
    /**
     * 批量删除媒体
     *
     * @param ids 媒体ID列表
     * @return 删除是否成功
     */
    boolean batchDeleteMedia(List<Long> ids);
    
    /**
     * 更新媒体排序
     *
     * @param mediaIds 媒体ID列表（按新的排序顺序）
     * @return 更新是否成功
     */
    boolean updateMediaSort(List<Long> mediaIds);
    
    /**
     * 获取媒体详情
     *
     * @param id 媒体ID
     * @return 媒体DTO
     */
    DemoSiteMediaDTO getMediaDetail(Long id);
}
