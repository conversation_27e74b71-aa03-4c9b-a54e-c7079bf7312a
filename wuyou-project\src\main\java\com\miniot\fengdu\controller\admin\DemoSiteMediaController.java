package com.miniot.fengdu.controller.admin;

import com.miniot.fengdu.common.ApiResponse;
import com.miniot.fengdu.common.BaseController;
import com.miniot.fengdu.dto.DemoSiteMediaDTO;
import com.miniot.fengdu.dto.DemoSiteMediaListDTO;
import com.miniot.fengdu.service.DemoSiteMediaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 示范点媒体管理控制器
 */
@RestController
@RequestMapping("/back/api/demo-site-media")
@Api(tags = "示范点媒体管理接口")
@Slf4j
public class DemoSiteMediaController extends BaseController {
    
    @Autowired
    private DemoSiteMediaService demoSiteMediaService;
    
    /**
     * 获取示范点媒体列表
     */
    @GetMapping("/{demoSiteId}")
    @ApiOperation(value = "获取示范点媒体列表", notes = "获取指定示范点的所有媒体文件")
    public ApiResponse<DemoSiteMediaListDTO> getDemoSiteMediaList(
            @ApiParam(value = "示范点ID", required = true) @PathVariable Long demoSiteId) {
        
        DemoSiteMediaListDTO result = demoSiteMediaService.getDemoSiteMediaList(demoSiteId);
        if (result == null) {
            return ApiResponse.fail("示范点不存在");
        }
        
        return ApiResponse.success(result, "获取媒体列表成功");
    }
    
    /**
     * 根据类型获取示范点媒体列表
     */
    @GetMapping("/{demoSiteId}/type/{mediaType}")
    @ApiOperation(value = "根据类型获取媒体列表", notes = "获取指定示范点的特定类型媒体文件")
    public ApiResponse<List<DemoSiteMediaDTO>> getDemoSiteMediaByType(
            @ApiParam(value = "示范点ID", required = true) @PathVariable Long demoSiteId,
            @ApiParam(value = "媒体类型", required = true, allowableValues = "image,video") @PathVariable String mediaType) {
        
        List<DemoSiteMediaDTO> result = demoSiteMediaService.getDemoSiteMediaByType(demoSiteId, mediaType);
        return ApiResponse.success(result, "获取媒体列表成功");
    }
    
    /**
     * 添加媒体
     */
    @PostMapping
    @ApiOperation(value = "添加媒体", notes = "为示范点添加新的媒体文件")
    public ApiResponse<Map<String, Long>> addMedia(
            @ApiParam(value = "媒体信息", required = true) @RequestBody @Validated DemoSiteMediaDTO demoSiteMediaDTO) {
        
        Long id = demoSiteMediaService.addMedia(demoSiteMediaDTO);
        
        Map<String, Long> result = new HashMap<>();
        result.put("id", id);
        
        return ApiResponse.success(result, "添加媒体成功");
    }
    
    /**
     * 获取媒体详情
     */
    @GetMapping("/detail/{id}")
    @ApiOperation(value = "获取媒体详情", notes = "根据ID获取媒体详细信息")
    public ApiResponse<DemoSiteMediaDTO> getMediaDetail(
            @ApiParam(value = "媒体ID", required = true) @PathVariable Long id) {
        
        DemoSiteMediaDTO result = demoSiteMediaService.getMediaDetail(id);
        if (result == null) {
            return ApiResponse.fail("媒体不存在");
        }
        
        return ApiResponse.success(result, "获取媒体详情成功");
    }
    
    /**
     * 更新媒体信息
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新媒体信息", notes = "更新指定ID的媒体信息")
    public ApiResponse<Object> updateMedia(
            @ApiParam(value = "媒体ID", required = true) @PathVariable Long id,
            @ApiParam(value = "媒体信息", required = true) @RequestBody @Validated DemoSiteMediaDTO demoSiteMediaDTO) {
        
        boolean success = demoSiteMediaService.updateMedia(id, demoSiteMediaDTO);
        if (!success) {
            return ApiResponse.fail("媒体不存在");
        }
        
        return ApiResponse.success(null, "更新媒体成功");
    }
    
    /**
     * 删除媒体
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除媒体", notes = "删除指定ID的媒体文件")
    public ApiResponse<Object> deleteMedia(
            @ApiParam(value = "媒体ID", required = true) @PathVariable Long id) {
        
        boolean success = demoSiteMediaService.deleteMedia(id);
        if (!success) {
            return ApiResponse.fail("媒体不存在");
        }
        
        return ApiResponse.success(null, "删除媒体成功");
    }
    
    /**
     * 批量删除媒体
     */
    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除媒体", notes = "批量删除多个媒体文件")
    public ApiResponse<Object> batchDeleteMedia(
            @ApiParam(value = "媒体ID列表", required = true) @RequestBody List<Long> ids) {

        boolean success = demoSiteMediaService.batchDeleteMedia(ids);
        if (!success) {
            return ApiResponse.fail("批量删除媒体失败");
        }
        return ApiResponse.success(null, "批量删除媒体成功");
    }
    
    /**
     * 更新媒体排序
     */
    @PutMapping("/sort")
    @ApiOperation(value = "更新媒体排序", notes = "更新媒体文件的排序顺序")
    public ApiResponse<Object> updateMediaSort(
            @ApiParam(value = "媒体ID列表（按新的排序顺序）", required = true) @RequestBody List<Long> mediaIds) {
        
        boolean success = demoSiteMediaService.updateMediaSort(mediaIds);
        if (!success) {
            return ApiResponse.fail("更新排序失败");
        }
        
        return ApiResponse.success(null, "更新排序成功");
    }
}
