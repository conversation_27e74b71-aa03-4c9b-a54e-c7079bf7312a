package com.miniot.fengdu.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 联通云OSS服务接口
 */
public interface OSSService {
    
    /**
     * 上传视频文件到OSS
     * @param file 视频文件
     * @param directory 存储目录
     * @return 上传结果，包含访问URL
     * @throws IOException 上传异常
     */
    Map<String, Object> uploadVideo(MultipartFile file, String directory) throws IOException;
    
    /**
     * 删除OSS上的文件（支持URL或ObjectKey）
     * @param fileUrlOrKey 文件URL或ObjectKey
     * @return 删除结果
     */
    boolean deleteFile(String fileUrlOrKey);

    /**
     * 获取文件访问URL
     * @param objectKey 对象键
     * @return 访问URL
     */
    String getFileUrl(String objectKey);

    /**
     * 生成预签名URL，用于上传文件
     * @param objectKey 对象键
     * @param expireTime 过期时间（秒）
     * @return 预签名URL
     */
    String generatePresignedUrl(String objectKey, long expireTime);

    /**
     * 创建新的Bucket
     * @param bucketName bucket名称
     * @return 是否创建成功
     */
    boolean createBucket(String bucketName);

    /**
     * 获取所有Bucket列表
     * @return Bucket列表
     */
    List<String> listBuckets();
}