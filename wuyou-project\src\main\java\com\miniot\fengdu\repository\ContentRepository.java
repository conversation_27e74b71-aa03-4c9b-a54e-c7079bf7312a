package com.miniot.fengdu.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miniot.fengdu.entity.Content;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 内容数据访问层
 */
@Mapper
public interface ContentRepository extends BaseMapper<Content> {
    
    /**
     * 分页查询内容列表（支持多条件筛选）
     */
    @Select("<script>"
            + "SELECT * FROM content WHERE 1=1 "
            + "<if test='type != null and type != \"\"'> AND type = #{type} </if>"
            + "<if test='status != null'> AND status = #{status} </if>"
            + "<if test='authorId != null'> AND author_id = #{authorId} </if>"
            + "<if test='title != null and title != \"\"'> AND title LIKE CONCAT('%', #{title}, '%') </if>"
            + "<if test='author != null and author != \"\"'> AND author LIKE CONCAT('%', #{author}, '%') </if>"
            + "</script>")
    Page<Content> findContentPage(Page<Content> page,
                                  @Param("type") String type,
                                  @Param("status") Integer status,
                                  @Param("authorId") Long authorId,
                                  @Param("title") String title,
                                  @Param("author") String author);
    
    /**
     * 查询待审核内容列表
     */
    @Select("SELECT * FROM content WHERE status = 2 ORDER BY created_at ASC")
    Page<Content> findPendingContent(Page<Content> page);
    
    /**
     * 根据状态统计内容数量
     */
    @Select("SELECT COUNT(*) FROM content WHERE status = #{status}")
    int countByStatus(@Param("status") Integer status);
    
    /**
     * 根据类型和状态统计内容数量
     */
    @Select("SELECT COUNT(*) FROM content WHERE type = #{type} AND status = #{status}")
    int countByTypeAndStatus(@Param("type") String type, @Param("status") Integer status);
    
    /**
     * 更新内容状态
     */
    @Update("UPDATE content SET status = #{status}, updated_at = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);
    
    /**
     * 审核通过（设置为已发布状态并更新发布时间）
     */
    @Update("UPDATE content SET status = 1, publish_time = NOW(), updated_at = NOW() WHERE id = #{id}")
    int approveContent(@Param("id") Long id);
    
    /**
     * 审核拒绝
     */
    @Update("UPDATE content SET status = 3, updated_at = NOW() WHERE id = #{id}")
    int rejectContent(@Param("id") Long id);
    
    /**
     * 增加浏览量
     */
    @Update("UPDATE content SET views = views + 1 WHERE id = #{id}")
    int incrementViews(@Param("id") Long id);
    
    /**
     * 增加点赞数
     */
    @Update("UPDATE content SET likes = likes + 1 WHERE id = #{id}")
    int incrementLikes(@Param("id") Long id);
    
    /**
     * 减少点赞数
     */
    @Update("UPDATE content SET likes = likes - 1 WHERE id = #{id} AND likes > 0")
    int decrementLikes(@Param("id") Long id);
    
    /**
     * 查询热门内容（按浏览量排序）
     */
    @Select("SELECT * FROM content WHERE status = 1 ORDER BY views DESC LIMIT #{limit}")
    Page<Content> findHotContent(Page<Content> page, @Param("limit") Integer limit);
    
    /**
     * 查询最新发布的内容
     */
    @Select("SELECT * FROM content WHERE status = 1 ORDER BY publish_time DESC LIMIT #{limit}")
    Page<Content> findLatestContent(Page<Content> page, @Param("limit") Integer limit);
}
