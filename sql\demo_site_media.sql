-- 示范点媒体表
CREATE TABLE `wy_demo_site_media` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '媒体ID',
  `demo_site_id` bigint(20) NOT NULL COMMENT '示范点ID',
  `media_type` varchar(20) NOT NULL COMMENT '媒体类型：image-图片，video-视频',
  `media_url` varchar(500) NOT NULL COMMENT '媒体文件URL',
  `sort_order` int(11) NOT NULL DEFAULT '1' COMMENT '排序顺序，数字越小越靠前',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_demo_site_id` (`demo_site_id`),
  KEY `idx_media_type` (`media_type`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示范点媒体表';

-- 为现有示范点添加示例媒体数据（可选）
-- INSERT INTO `wy_demo_site_media` (`demo_site_id`, `media_type`, `media_url`, `sort_order`) VALUES
-- (1, 'image', 'https://wuyou.obs-cq.cucloud.cn/demo-sites/2024/01/15/example1.jpg', 1),
-- (1, 'image', 'https://wuyou.obs-cq.cucloud.cn/demo-sites/2024/01/15/example2.jpg', 2),
-- (1, 'video', 'https://wuyou.obs-cq.cucloud.cn/demo-sites/2024/01/15/example.mp4', 3);
