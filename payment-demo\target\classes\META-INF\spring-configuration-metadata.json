{"groups": [{"name": "miniprogram", "type": "com.starlight.paymentdemo.config.MiniProgramConfig", "sourceType": "com.starlight.paymentdemo.config.MiniProgramConfig"}, {"name": "wxpay", "type": "com.starlight.paymentdemo.config.WxPayConfig", "sourceType": "com.starlight.paymentdemo.config.WxPayConfig"}], "properties": [{"name": "miniprogram.base-url", "type": "java.lang.String", "description": "小程序服务基础URL", "sourceType": "com.starlight.paymentdemo.config.MiniProgramConfig", "defaultValue": "http://localhost:8999"}, {"name": "miniprogram.cart-order-detail-path", "type": "java.lang.String", "description": "获取购物车订单详情的API路径", "sourceType": "com.starlight.paymentdemo.config.MiniProgramConfig", "defaultValue": "/api/cart-order/detail"}, {"name": "miniprogram.connect-timeout", "type": "java.lang.Integer", "description": "连接超时时间（毫秒）", "sourceType": "com.starlight.paymentdemo.config.MiniProgramConfig", "defaultValue": 5000}, {"name": "miniprogram.read-timeout", "type": "java.lang.Integer", "description": "读取超时时间（毫秒）", "sourceType": "com.starlight.paymentdemo.config.MiniProgramConfig", "defaultValue": 10000}, {"name": "wxpay.api-v3-key", "type": "java.lang.String", "sourceType": "com.starlight.paymentdemo.config.WxPayConfig"}, {"name": "wxpay.app-secret", "type": "java.lang.String", "sourceType": "com.starlight.paymentdemo.config.WxPayConfig"}, {"name": "wxpay.appid", "type": "java.lang.String", "sourceType": "com.starlight.paymentdemo.config.WxPayConfig"}, {"name": "wxpay.cert-path", "type": "java.lang.String", "sourceType": "com.starlight.paymentdemo.config.WxPayConfig"}, {"name": "wxpay.domain", "type": "java.lang.String", "sourceType": "com.starlight.paymentdemo.config.WxPayConfig"}, {"name": "wxpay.domain2", "type": "java.lang.String", "sourceType": "com.starlight.paymentdemo.config.WxPayConfig"}, {"name": "wxpay.mch-id", "type": "java.lang.String", "sourceType": "com.starlight.paymentdemo.config.WxPayConfig"}, {"name": "wxpay.mch-serial-no", "type": "java.lang.String", "sourceType": "com.starlight.paymentdemo.config.WxPayConfig"}, {"name": "wxpay.notify-domain", "type": "java.lang.String", "sourceType": "com.starlight.paymentdemo.config.WxPayConfig"}, {"name": "wxpay.partner-key", "type": "java.lang.String", "sourceType": "com.starlight.paymentdemo.config.WxPayConfig"}, {"name": "wxpay.private-key-path", "type": "java.lang.String", "sourceType": "com.starlight.paymentdemo.config.WxPayConfig"}], "hints": []}