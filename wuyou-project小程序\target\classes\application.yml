spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  datasource:
#    url: **********************************************************************************************************
    url: *******************************************************************************************************************************************
    username: root
    password: mikelei@2025
    driver-class-name: com.mysql.cj.jdbc.Driver
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher # Springfox 3.0.0 需要此配置
  # Redis配置
  redis:
    host: **************
    port: 16380
    password: mikelei.2024
    database: 0
    timeout: 3000
    
  # MyBatis-Plus 配置
mybatis-plus:
    configuration:
      map-underscore-to-camel-case: true
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 配置扫描的包路径
    mapper-locations: classpath:/mapper/**/*.xml
    type-aliases-package: com.miniot.fengdu.entity  # 你自己的实体类路径
    global-config:
      db-config:
        id-type: auto  # 主键策略，自增长
        logic-delete-value: 1  # 逻辑删除值
        logic-not-delete-value: 0  # 逻辑未删除值
        field-strategy: not_empty  # 字段策略，选择性更新时，空值不更新

server:
  port: 8999

app:
  content-config:
    content1: true
    content2: true
    content3: "社交分享"

# 支付服务配置
payment:
  service:
    base-url: http://localhost:8989
    jsapi-path: /wxapi/wx-pay-v2/jsapi
    query-path: /wxapi/wx-pay-v2/query
    close-path: /wxapi/wx-pay-v2/close
    default-product-id: 1
    connect-timeout: 5000
    read-timeout: 10000