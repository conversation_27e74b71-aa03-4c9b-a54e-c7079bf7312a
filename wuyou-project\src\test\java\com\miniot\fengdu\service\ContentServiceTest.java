package com.miniot.fengdu.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miniot.fengdu.entity.Content;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;

/**
 * 内容服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class ContentServiceTest {

    @Autowired
    private ContentService contentService;

    @Test
    public void testCreateContent() {
        // 创建测试内容
        Content content = new Content();
        content.setTitle("测试文章标题");
        content.setType("article");
        content.setCover("https://example.com/cover.jpg");
        content.setSummary("这是一篇测试文章的摘要");
        content.setContent("这是测试文章的详细内容...");
        content.setAuthor("测试管理员");
        content.setAuthorId(0L);
        content.setTag("测试,管理");

        boolean result = contentService.createContent(content);
        System.out.println("创建内容结果: " + result);
        System.out.println("内容ID: " + content.getId());
        
        assert result : "内容创建应该成功";
        assert content.getId() != null : "内容ID应该不为空";
        assert content.getStatus() == 1 : "管理员创建的内容状态应该为1（已发布）";
        assert content.getPublishTime() != null : "发布时间应该不为空";
    }

    @Test
    public void testGetContentPage() {
        // 测试分页查询
        Page<Content> page = contentService.getContentPage(1, 10, null, null, null, null, null, "updated_at");
        
        System.out.println("总记录数: " + page.getTotal());
        System.out.println("当前页记录数: " + page.getRecords().size());
        
        for (Content content : page.getRecords()) {
            System.out.println("内容: " + content.getTitle() + " - 状态: " + content.getStatus());
        }
    }

    @Test
    public void testAuditContent() {
        // 先创建一个待审核的内容
        Content content = new Content();
        content.setTitle("待审核测试文章");
        content.setType("article");
        content.setCover("https://example.com/cover.jpg");
        content.setSummary("这是一篇待审核的测试文章");
        content.setContent("待审核内容详情...");
        content.setAuthor("测试用户");
        content.setAuthorId(1001L);
        content.setStatus(2); // 设置为待审核状态
        content.setCreatedAt(LocalDateTime.now());
        content.setUpdatedAt(LocalDateTime.now());
        content.setPublishTime(LocalDateTime.now()); // 设置临时发布时间
        content.setViews(0);
        content.setLikes(0);

        boolean created = contentService.save(content);
        System.out.println("创建待审核内容: " + created);

        if (created) {
            // 验证内容创建成功
            Content savedContent = contentService.getById(content.getId());
            System.out.println("创建后状态: " + savedContent.getStatus());

            assert savedContent != null : "内容应该创建成功";
            assert savedContent.getStatus() == 2 : "初始状态应该为待审核";
        }
    }

    @Test
    public void testGetPendingContent() {
        // 通过列表接口筛选待审核内容
        Page<Content> pendingPage = contentService.getContentPage(1, 10, null, 2, null, null, null, null);

        System.out.println("待审核内容数量: " + pendingPage.getTotal());

        for (Content content : pendingPage.getRecords()) {
            System.out.println("待审核内容: " + content.getTitle() + " - 作者: " + content.getAuthor());
            assert content.getStatus() == 2 : "待审核列表中的内容状态应该为2";
        }
    }

    @Test
    public void testGetAllContent() {
        // 测试查询所有数据（不分页）
        Page<Content> allContentPage = contentService.getContentPage(1, Integer.MAX_VALUE, null, null, null, null, null, "updated_at");

        System.out.println("查询所有数据 - 总记录数: " + allContentPage.getTotal());
        System.out.println("查询所有数据 - 当前页记录数: " + allContentPage.getRecords().size());
        System.out.println("是否查询了所有数据: " + (allContentPage.getRecords().size() == allContentPage.getTotal()));

        // 验证查询结果
        assert allContentPage.getRecords().size() == allContentPage.getTotal() : "应该查询所有数据";
    }
}
