<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.miniot.fengdu.entity.BannerVideoTest" time="0.028" tests="2" errors="0" skipped="0" failures="0">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\CQGW2025\潼南五有\wuyou-project\target\test-classes;D:\CQGW2025\潼南五有\wuyou-project\target\classes;D:\apache-maven-3.8.6-bin\maven-repository\com\baomidou\mybatis-plus-boot-starter\3.5.0\mybatis-plus-boot-starter-3.5.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\baomidou\mybatis-plus\3.5.0\mybatis-plus-3.5.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-autoconfigure\2.6.13\spring-boot-autoconfigure-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter-jdbc\2.6.13\spring-boot-starter-jdbc-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-jdbc\5.3.23\spring-jdbc-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter-web\2.6.13\spring-boot-starter-web-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter\2.6.13\spring-boot-starter-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter-logging\2.6.13\spring-boot-starter-logging-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;D:\apache-maven-3.8.6-bin\maven-repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;D:\apache-maven-3.8.6-bin\maven-repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\yaml\snakeyaml\1.29\snakeyaml-1.29.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter-json\2.6.13\spring-boot-starter-json-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.4\jackson-datatype-jdk8-2.13.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.4\jackson-datatype-jsr310-2.13.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.4\jackson-module-parameter-names-2.13.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter-tomcat\2.6.13\spring-boot-starter-tomcat-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.68\tomcat-embed-core-9.0.68.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.68\tomcat-embed-websocket-9.0.68.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-web\5.3.23\spring-web-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-beans\5.3.23\spring-beans-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-webmvc\5.3.23\spring-webmvc-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-aop\5.3.23\spring-aop-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-context\5.3.23\spring-context-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-expression\5.3.23\spring-expression-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\fasterxml\jackson\core\jackson-databind\2.13.5\jackson-databind-2.13.5.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\fasterxml\jackson\core\jackson-annotations\2.13.4\jackson-annotations-2.13.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\fasterxml\jackson\core\jackson-core\2.13.4\jackson-core-2.13.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\baomidou\mybatis-plus-extension\3.5.3.1\mybatis-plus-extension-3.5.3.1.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\baomidou\mybatis-plus-core\3.5.3.1\mybatis-plus-core-3.5.3.1.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\baomidou\mybatis-plus-annotation\3.5.3.1\mybatis-plus-annotation-3.5.3.1.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\github\jsqlparser\jsqlparser\4.4\jsqlparser-4.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\mybatis\mybatis\3.5.10\mybatis-3.5.10.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\mybatis\mybatis-spring\2.0.7\mybatis-spring-2.0.7.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\jsoup\jsoup\1.15.3\jsoup-1.15.3.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter-validation\2.6.13\spring-boot-starter-validation-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.68\tomcat-embed-el-9.0.68.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\alibaba\easyexcel\3.3.2\easyexcel-3.3.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\alibaba\easyexcel-core\3.3.2\easyexcel-core-3.3.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\alibaba\easyexcel-support\3.3.2\easyexcel-support-3.3.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\poi\poi\4.1.2\poi-4.1.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\zaxxer\SparseBitSet\1.2\SparseBitSet-1.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\poi\poi-ooxml\4.1.2\poi-ooxml-4.1.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\commons\commons-compress\1.19\commons-compress-1.19.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\github\virtuald\curvesapi\1.06\curvesapi-1.06.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\poi\poi-ooxml-schemas\4.1.2\poi-ooxml-schemas-4.1.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\xmlbeans\xmlbeans\3.1.0\xmlbeans-3.1.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\commons\commons-csv\1.8\commons-csv-1.8.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\ehcache\ehcache\3.9.10\ehcache-3.9.10.jar;D:\apache-maven-3.8.6-bin\maven-repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\amazonaws\aws-java-sdk-core\1.11.24\aws-java-sdk-core-1.11.24.jar;D:\apache-maven-3.8.6-bin\maven-repository\commons-logging\commons-logging\1.1.3\commons-logging-1.1.3.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\httpcomponents\httpcore\4.4.15\httpcore-4.4.15.jar;D:\apache-maven-3.8.6-bin\maven-repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\fasterxml\jackson\dataformat\jackson-dataformat-cbor\2.13.4\jackson-dataformat-cbor-2.13.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\joda-time\joda-time\2.8.1\joda-time-2.8.1.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\amazonaws\aws-java-sdk-s3\1.11.24\aws-java-sdk-s3-1.11.24.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\amazonaws\aws-java-sdk-kms\1.11.24\aws-java-sdk-kms-1.11.24.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-devtools\2.6.13\spring-boot-devtools-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot\2.6.13\spring-boot-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\projectlombok\lombok\1.18.22\lombok-1.18.22.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-boot-starter\3.0.0\springfox-boot-starter-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-oas\3.0.0\springfox-oas-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\swagger\core\v3\swagger-annotations\2.1.2\swagger-annotations-2.1.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\swagger\core\v3\swagger-models\2.1.2\swagger-models-2.1.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-spi\3.0.0\springfox-spi-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-schema\3.0.0\springfox-schema-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-core\3.0.0\springfox-core-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-spring-web\3.0.0\springfox-spring-web-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\github\classgraph\classgraph\4.8.83\classgraph-4.8.83.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-spring-webmvc\3.0.0\springfox-spring-webmvc-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-spring-webflux\3.0.0\springfox-spring-webflux-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-swagger-common\3.0.0\springfox-swagger-common-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\mapstruct\mapstruct\1.3.1.Final\mapstruct-1.3.1.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-data-rest\3.0.0\springfox-data-rest-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-bean-validators\3.0.0\springfox-bean-validators-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-swagger2\3.0.0\springfox-swagger2-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\swagger\swagger-annotations\1.5.20\swagger-annotations-1.5.20.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\swagger\swagger-models\1.5.20\swagger-models-1.5.20.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-swagger-ui\3.0.0\springfox-swagger-ui-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\plugin\spring-plugin-metadata\2.0.0.RELEASE\spring-plugin-metadata-2.0.0.RELEASE.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter-test\2.6.13\spring-boot-starter-test-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-test\2.6.13\spring-boot-test-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-test-autoconfigure\2.6.13\spring-boot-test-autoconfigure-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\jayway\jsonpath\json-path\2.6.0\json-path-2.6.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;D:\apache-maven-3.8.6-bin\maven-repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\ow2\asm\asm\9.1\asm-9.1.jar;D:\apache-maven-3.8.6-bin\maven-repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\apache-maven-3.8.6-bin\maven-repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\assertj\assertj-core\3.21.0\assertj-core-3.21.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\mockito\mockito-core\4.0.0\mockito-core-4.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\net\bytebuddy\byte-buddy\1.11.22\byte-buddy-1.11.22.jar;D:\apache-maven-3.8.6-bin\maven-repository\net\bytebuddy\byte-buddy-agent\1.11.22\byte-buddy-agent-1.11.22.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\mockito\mockito-junit-jupiter\4.0.0\mockito-junit-jupiter-4.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-core\5.3.23\spring-core-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-jcl\5.3.23\spring-jcl-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-test\5.3.23\spring-test-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\xmlunit\xmlunit-core\2.8.4\xmlunit-core-2.8.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter-data-redis\2.6.13\spring-boot-starter-data-redis-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\data\spring-data-redis\2.6.9\spring-data-redis-2.6.9.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\data\spring-data-keyvalue\2.6.9\spring-data-keyvalue-2.6.9.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\data\spring-data-commons\2.6.9\spring-data-commons-2.6.9.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-tx\5.3.23\spring-tx-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-oxm\5.3.23\spring-oxm-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-context-support\5.3.23\spring-context-support-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\lettuce\lettuce-core\6.1.10.RELEASE\lettuce-core-6.1.10.RELEASE.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\netty\netty-common\4.1.84.Final\netty-common-4.1.84.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\netty\netty-handler\4.1.84.Final\netty-handler-4.1.84.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\netty\netty-resolver\4.1.84.Final\netty-resolver-4.1.84.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\netty\netty-buffer\4.1.84.Final\netty-buffer-4.1.84.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\netty\netty-transport-native-unix-common\4.1.84.Final\netty-transport-native-unix-common-4.1.84.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\netty\netty-codec\4.1.84.Final\netty-codec-4.1.84.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\netty\netty-transport\4.1.84.Final\netty-transport-4.1.84.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\projectreactor\reactor-core\3.4.24\reactor-core-3.4.24.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 10"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="D:\java\jdk1.8.0_221\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire4982204902924671223\surefirebooter5708958871374412703.jar C:\Users\<USER>\AppData\Local\Temp\surefire4982204902924671223 2025-08-01T10-17-56_328-jvmRun1 surefire958186475734768143tmp surefire_05135511935099398521tmp"/>
    <property name="test" value="BannerVideoTest"/>
    <property name="surefire.test.class.path" value="D:\CQGW2025\潼南五有\wuyou-project\target\test-classes;D:\CQGW2025\潼南五有\wuyou-project\target\classes;D:\apache-maven-3.8.6-bin\maven-repository\com\baomidou\mybatis-plus-boot-starter\3.5.0\mybatis-plus-boot-starter-3.5.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\baomidou\mybatis-plus\3.5.0\mybatis-plus-3.5.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-autoconfigure\2.6.13\spring-boot-autoconfigure-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter-jdbc\2.6.13\spring-boot-starter-jdbc-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-jdbc\5.3.23\spring-jdbc-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter-web\2.6.13\spring-boot-starter-web-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter\2.6.13\spring-boot-starter-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter-logging\2.6.13\spring-boot-starter-logging-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;D:\apache-maven-3.8.6-bin\maven-repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;D:\apache-maven-3.8.6-bin\maven-repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\yaml\snakeyaml\1.29\snakeyaml-1.29.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter-json\2.6.13\spring-boot-starter-json-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.4\jackson-datatype-jdk8-2.13.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.4\jackson-datatype-jsr310-2.13.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.4\jackson-module-parameter-names-2.13.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter-tomcat\2.6.13\spring-boot-starter-tomcat-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.68\tomcat-embed-core-9.0.68.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.68\tomcat-embed-websocket-9.0.68.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-web\5.3.23\spring-web-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-beans\5.3.23\spring-beans-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-webmvc\5.3.23\spring-webmvc-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-aop\5.3.23\spring-aop-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-context\5.3.23\spring-context-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-expression\5.3.23\spring-expression-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\fasterxml\jackson\core\jackson-databind\2.13.5\jackson-databind-2.13.5.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\fasterxml\jackson\core\jackson-annotations\2.13.4\jackson-annotations-2.13.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\fasterxml\jackson\core\jackson-core\2.13.4\jackson-core-2.13.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\baomidou\mybatis-plus-extension\3.5.3.1\mybatis-plus-extension-3.5.3.1.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\baomidou\mybatis-plus-core\3.5.3.1\mybatis-plus-core-3.5.3.1.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\baomidou\mybatis-plus-annotation\3.5.3.1\mybatis-plus-annotation-3.5.3.1.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\github\jsqlparser\jsqlparser\4.4\jsqlparser-4.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\mybatis\mybatis\3.5.10\mybatis-3.5.10.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\mybatis\mybatis-spring\2.0.7\mybatis-spring-2.0.7.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\jsoup\jsoup\1.15.3\jsoup-1.15.3.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter-validation\2.6.13\spring-boot-starter-validation-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.68\tomcat-embed-el-9.0.68.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\alibaba\easyexcel\3.3.2\easyexcel-3.3.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\alibaba\easyexcel-core\3.3.2\easyexcel-core-3.3.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\alibaba\easyexcel-support\3.3.2\easyexcel-support-3.3.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\poi\poi\4.1.2\poi-4.1.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\zaxxer\SparseBitSet\1.2\SparseBitSet-1.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\poi\poi-ooxml\4.1.2\poi-ooxml-4.1.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\commons\commons-compress\1.19\commons-compress-1.19.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\github\virtuald\curvesapi\1.06\curvesapi-1.06.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\poi\poi-ooxml-schemas\4.1.2\poi-ooxml-schemas-4.1.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\xmlbeans\xmlbeans\3.1.0\xmlbeans-3.1.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\commons\commons-csv\1.8\commons-csv-1.8.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\ehcache\ehcache\3.9.10\ehcache-3.9.10.jar;D:\apache-maven-3.8.6-bin\maven-repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\amazonaws\aws-java-sdk-core\1.11.24\aws-java-sdk-core-1.11.24.jar;D:\apache-maven-3.8.6-bin\maven-repository\commons-logging\commons-logging\1.1.3\commons-logging-1.1.3.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apache\httpcomponents\httpcore\4.4.15\httpcore-4.4.15.jar;D:\apache-maven-3.8.6-bin\maven-repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\fasterxml\jackson\dataformat\jackson-dataformat-cbor\2.13.4\jackson-dataformat-cbor-2.13.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\joda-time\joda-time\2.8.1\joda-time-2.8.1.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\amazonaws\aws-java-sdk-s3\1.11.24\aws-java-sdk-s3-1.11.24.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\amazonaws\aws-java-sdk-kms\1.11.24\aws-java-sdk-kms-1.11.24.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-devtools\2.6.13\spring-boot-devtools-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot\2.6.13\spring-boot-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\projectlombok\lombok\1.18.22\lombok-1.18.22.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-boot-starter\3.0.0\springfox-boot-starter-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-oas\3.0.0\springfox-oas-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\swagger\core\v3\swagger-annotations\2.1.2\swagger-annotations-2.1.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\swagger\core\v3\swagger-models\2.1.2\swagger-models-2.1.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-spi\3.0.0\springfox-spi-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-schema\3.0.0\springfox-schema-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-core\3.0.0\springfox-core-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-spring-web\3.0.0\springfox-spring-web-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\github\classgraph\classgraph\4.8.83\classgraph-4.8.83.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-spring-webmvc\3.0.0\springfox-spring-webmvc-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-spring-webflux\3.0.0\springfox-spring-webflux-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-swagger-common\3.0.0\springfox-swagger-common-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\mapstruct\mapstruct\1.3.1.Final\mapstruct-1.3.1.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-data-rest\3.0.0\springfox-data-rest-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-bean-validators\3.0.0\springfox-bean-validators-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-swagger2\3.0.0\springfox-swagger2-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\swagger\swagger-annotations\1.5.20\swagger-annotations-1.5.20.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\swagger\swagger-models\1.5.20\swagger-models-1.5.20.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\springfox\springfox-swagger-ui\3.0.0\springfox-swagger-ui-3.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\plugin\spring-plugin-metadata\2.0.0.RELEASE\spring-plugin-metadata-2.0.0.RELEASE.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter-test\2.6.13\spring-boot-starter-test-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-test\2.6.13\spring-boot-test-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-test-autoconfigure\2.6.13\spring-boot-test-autoconfigure-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\jayway\jsonpath\json-path\2.6.0\json-path-2.6.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;D:\apache-maven-3.8.6-bin\maven-repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\ow2\asm\asm\9.1\asm-9.1.jar;D:\apache-maven-3.8.6-bin\maven-repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\apache-maven-3.8.6-bin\maven-repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\assertj\assertj-core\3.21.0\assertj-core-3.21.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\mockito\mockito-core\4.0.0\mockito-core-4.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\net\bytebuddy\byte-buddy\1.11.22\byte-buddy-1.11.22.jar;D:\apache-maven-3.8.6-bin\maven-repository\net\bytebuddy\byte-buddy-agent\1.11.22\byte-buddy-agent-1.11.22.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\mockito\mockito-junit-jupiter\4.0.0\mockito-junit-jupiter-4.0.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\apache-maven-3.8.6-bin\maven-repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-core\5.3.23\spring-core-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-jcl\5.3.23\spring-jcl-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-test\5.3.23\spring-test-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\xmlunit\xmlunit-core\2.8.4\xmlunit-core-2.8.4.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\boot\spring-boot-starter-data-redis\2.6.13\spring-boot-starter-data-redis-2.6.13.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\data\spring-data-redis\2.6.9\spring-data-redis-2.6.9.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\data\spring-data-keyvalue\2.6.9\spring-data-keyvalue-2.6.9.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\data\spring-data-commons\2.6.9\spring-data-commons-2.6.9.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-tx\5.3.23\spring-tx-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-oxm\5.3.23\spring-oxm-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\springframework\spring-context-support\5.3.23\spring-context-support-5.3.23.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\lettuce\lettuce-core\6.1.10.RELEASE\lettuce-core-6.1.10.RELEASE.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\netty\netty-common\4.1.84.Final\netty-common-4.1.84.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\netty\netty-handler\4.1.84.Final\netty-handler-4.1.84.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\netty\netty-resolver\4.1.84.Final\netty-resolver-4.1.84.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\netty\netty-buffer\4.1.84.Final\netty-buffer-4.1.84.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\netty\netty-transport-native-unix-common\4.1.84.Final\netty-transport-native-unix-common-4.1.84.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\netty\netty-codec\4.1.84.Final\netty-codec-4.1.84.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\netty\netty-transport\4.1.84.Final\netty-transport-4.1.84.Final.jar;D:\apache-maven-3.8.6-bin\maven-repository\io\projectreactor\reactor-core\3.4.24\reactor-core-3.4.24.jar;D:\apache-maven-3.8.6-bin\maven-repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\java\jdk1.8.0_221\jre"/>
    <property name="basedir" value="D:\CQGW2025\潼南五有\wuyou-project"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire4982204902924671223\surefirebooter5708958871374412703.jar"/>
    <property name="sun.boot.class.path" value="D:\java\jdk1.8.0_221\jre\lib\resources.jar;D:\java\jdk1.8.0_221\jre\lib\rt.jar;D:\java\jdk1.8.0_221\jre\lib\sunrsasign.jar;D:\java\jdk1.8.0_221\jre\lib\jsse.jar;D:\java\jdk1.8.0_221\jre\lib\jce.jar;D:\java\jdk1.8.0_221\jre\lib\charsets.jar;D:\java\jdk1.8.0_221\jre\lib\jfr.jar;D:\java\jdk1.8.0_221\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="1.8.0_221-b11"/>
    <property name="user.name" value="BLANKPINK"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="D:\java\jdk1.8.0_221\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="D:\apache-maven-3.8.6-bin\maven-repository"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="1.8.0_221"/>
    <property name="user.dir" value="D:\CQGW2025\潼南五有\wuyou-project"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="D:\java\jdk1.8.0_221\jre\bin;C:\windows\Sun\Java\bin;C:\windows\system32;C:\windows;D:\apache-maven-3.8.6-bin\apache-maven-3.8.6\bin;d:\cursor\resources\app\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\dotnet\;D:\java\jdk1.8.0_221\bin;D:\java\jdk1.8.0_221\jre\bin;D:\MySQL\MySQL Server 8.0\MySQL\bin;D:\软件\bin;C;\Users\BLANKPINK\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;D:\cursor\resources\app\bin;d:\cursor\resources\app\bin;D:\java\nvm;D:\java\nodejs;D:\java\nvm\v18.19.0\node_global\node_modules;D:\java\nodejs\node_modules\Yarn\bin\;D:\Git\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\java\nvm;D:\java\nodejs;C:\Users\<USER>\AppData\Local\Yarn\bin;D:\软件\Microsoft VS Code\bin;D:\cursor\cursor\resources\app\bin;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="25.221-b11"/>
    <property name="java.ext.dirs" value="D:\java\jdk1.8.0_221\jre\lib\ext;C:\windows\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
  <testcase name="testBannerWithImageAndVideo" classname="com.miniot.fengdu.entity.BannerVideoTest" time="0.019"/>
  <testcase name="testBannerVideoField" classname="com.miniot.fengdu.entity.BannerVideoTest" time="0.001"/>
</testsuite>