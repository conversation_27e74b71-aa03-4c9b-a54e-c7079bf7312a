package com.miniot.fengdu.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.miniot.fengdu.entity.DemoSiteMedia;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 示范点媒体数据访问层
 */
@Mapper
@Repository
public interface DemoSiteMediaRepository extends BaseMapper<DemoSiteMedia> {
    
    /**
     * 根据示范点ID查询媒体列表，按排序顺序排列
     * @param demoSiteId 示范点ID
     * @return 媒体列表
     */
    @Select("SELECT * FROM wy_demo_site_media WHERE demo_site_id = #{demoSiteId} ORDER BY sort_order ASC, id ASC")
    List<DemoSiteMedia> findByDemoSiteIdOrderBySortOrder(@Param("demoSiteId") Long demoSiteId);
}
